<?php

declare(strict_types=1);

namespace Common\DataSources;

use Common\Classes\DataProvider\Source;
use Common\Models\DrawingNodeType;

/**
 * Class DrawingNodeTypeSource
 *
 * @package Common\DataSources
 */
class DrawingNodeTypeSource extends Source
{
    /**
     * @var string|null Name of source
     */
    protected ?string $name = 'drawing_node_types';

    /**
     * Setup source by importing data into database
     */
    public function setup(): void
    {
        $types = $this->loadData();
        foreach ($types as $id => $type) {
            DrawingNodeType::create([
                'drawingNodeTypeID' => $id,
                'name' => $type['name'],
                'showInLegend' => $type['show_in_legend'] ?? true,
                'version' => $type['version'] ?? 1,
                'isInUse' => $type['is_in_use'] ?? true
            ]);
        }
    }
}
