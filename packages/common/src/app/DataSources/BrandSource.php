<?php

declare(strict_types=1);

namespace Common\DataSources;

use Common\Classes\DataProvider\Source;
use Common\Models\Brand;
use Ramsey\Uuid\Uuid;

/**
 * Class BrandSource
 *
 * @package Common\DataSources
 */
class BrandSource extends Source
{
    /**
     * @var string|null Name of source
     */
    protected ?string $name = 'brands';

    /**
     * Setup source by importing data into database
     *
     * @param string $env
     * @param SmtpCredentialSource $smtp_credential
     * @param TextMessageServiceSource $text_message_service
     * @throws \Core\Exceptions\AppException
     */
    public function setup(string $env, SmtpCredentialSource $smtp_credential, TextMessageServiceSource $text_message_service): void
    {
        $brands = $this->loadData($env);
        $alias_map = [
            'id' => [],
            'uuid' => []
        ];
        foreach ($brands as $alias => $brand) {
            $uuid = Uuid::uuid4();
            $data = [
                'brandUUID' => $uuid->getBytes(),
                'name' => $brand['name'],
                'slug' => $alias,
                'signUpUrl' => isset($brand['signup_url']) ? $brand['signup_url'] : null,
                'mailDomain' => $brand['mail_domain'],
                'mailFromAddress' => $brand['mail_from_address'] ?? "system@{$brand['mail_domain']}",
                'mailReplyAddress' => $brand['mail_reply_address'] ?? "system@{$brand['mail_domain']}",
                'mailColor' => $brand['mail_color'],
                'smtpCredentialID' => $smtp_credential->getIdFromAlias($brand['smtp_credential_alias'] ?? 'main'),
                'textMessageServiceID' => $text_message_service->getUuidFromAlias($brand['text_message_service_alias'] ?? 'main')->getBytes()
            ];
            if (isset($brand['id'])) {
                $data['brandID'] = $brand['id'];
            }
            /** @var Brand $model */
            $model = Brand::create($data);
            $alias_map['id'][$alias] = $model->getKey();
            $alias_map['uuid'][$alias] = $uuid;
        }
        $this->storeIdAliasMap($alias_map['id']);
        $this->storeUuidAliasMap($alias_map['uuid']);
    }
}
