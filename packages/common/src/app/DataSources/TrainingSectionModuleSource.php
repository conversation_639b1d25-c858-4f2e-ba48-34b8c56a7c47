<?php

declare(strict_types=1);

namespace Common\DataSources;

use App\Classes\Template;
use Common\Classes\DataProvider\Source;
use Common\Classes\Faker;
use Common\Models\TrainingSectionModule;

/**
 * Class TrainingSectionModuleSource
 *
 * @package Common\DataSources
 */
class TrainingSectionModuleSource extends Source
{
    /**
     * @var string|null Name of source
     */
    protected ?string $name = 'training_section_modules';

    /**
     * Setup source by importing data into database
     *
     * @param string $env
     * @param TrainingSectionSource $training_section
     * @throws \Core\Exceptions\AppException
     */
    public function setup(string $env, TrainingSectionSource $training_section): void
    {
        $faker = Faker::create();

        $modules = $this->loadData($env);
        $order = 1;
        foreach ($modules as $alias => $module) {
            $intro = $module['intro'] ?? null;
            if ($intro !== null && isset($module['intro_vars'])) {
                $intro = Template::replace($intro, $module['intro_vars'], '{{', '}}');
            }

            $content = $module['content'] ?? null;
            if ($content !== null && isset($module['content_vars'])) {
                $content = Template::replace($content, $module['content_vars'], '{{', '}}');
            }

            /** @var TrainingSectionModule $model */
            $model = TrainingSectionModule::create([
                'trainingSectionID' => $training_section->getIdFromAlias($module['section_alias']),
                'title' => $module['title'],
                'cardTitle' => $module['card_title'] ?? null,
                'cardIcon' => $module['card_icon'] ?? null,
                'alias' => $alias,
                'intro' => $intro,
                'youtubeVideoID' => $module['youtube_video_id'] ?? null,
                'youtubeVideoStartTime' => $module['youtube_video_start_time'] ?? null,
                'imageUrl' => $module['image_url'] ?? null,
                'content' => $content,
                'tryUrl' => $module['try_url'],
                'helpCenterUrl' => $module['help_center_url'] ?? $faker->url,
                'status' => TrainingSectionModule::STATUS_ACTIVE,
                'accessLevel' => $module['access_level'],
                'isRequired' => $module['is_required'] ?? false,
                'isHidden' => $module['is_hidden'] ?? false,
                'requiredActionCount' => $module['required_action_count'] ?? count($module['actions']),
                'order' => $order++
            ]);
            $model->actions()->attach($module['actions']);
        }
    }
}
