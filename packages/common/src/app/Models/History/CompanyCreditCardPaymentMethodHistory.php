<?php

namespace Common\Models\History;

use Common\Classes\DB\HistoryModel;
use Common\Models\Common\CompanyCreditCardPaymentMethodCommon;
use Common\Models\Interfaces\CompanyCreditCardPaymentMethodInterface;

class CompanyCreditCardPaymentMethodHistory extends HistoryModel implements CompanyCreditCardPaymentMethodInterface
{
    use CompanyCreditCardPaymentMethodCommon;

    protected $table = 'companyCreditCardPaymentMethodsHistory';
    protected $primaryKey = 'companyCreditCardPaymentMethodsHistoryID';
    protected $entityPrimaryKey = 'companyCreditCardPaymentMethodID';
}
