<?php

namespace Common\Models\History;

use Common\Classes\DB\HistoryModel;
use Common\Models\Common\BidItemCustomDrawingCommon;
use Common\Models\Interfaces\BidItemCustomDrawingInterface;

class BidItemCustomDrawingHistory extends HistoryModel implements BidItemCustomDrawingInterface
{
    use BidItemCustomDrawingCommon;

    protected $table = 'bidItemCustomDrawingsHistory';
    protected $primaryKey = 'bidItemCustomDrawingsHistoryID';
    protected $entityPrimaryKey = 'bidItemCustomDrawingID';
}
