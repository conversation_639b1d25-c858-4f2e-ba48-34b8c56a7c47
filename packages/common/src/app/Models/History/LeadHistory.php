<?php

namespace Common\Models\History;

use Common\Classes\DB\HistoryModel;
use Common\Interfaces\DB\ScopeSearchInterface;
use Common\Models\Common\LeadCommon;
use Common\Models\Interfaces\LeadInterface;

class LeadHistory extends HistoryModel implements LeadInterface, ScopeSearchInterface
{
    use LeadCommon;

    protected $table = 'leadsHistory';
    protected $primaryKey = 'leadsHistoryID';
    protected $entityPrimaryKey = 'leadID';
}
