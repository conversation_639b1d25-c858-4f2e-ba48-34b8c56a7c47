<?php

namespace Common\Models;

use Common\Classes\DB\HistoryEntityModel;

class BidItemProductMaterialLineItem extends HistoryEntityModel implements Interfaces\BidItemProductMaterialLineItemInterface
{
    use Common\BidItemProductMaterialLineItemCommon;

    protected $table = 'bidItemProductMaterialLineItems';
    protected $primaryKey = 'bidItemProductMaterialLineItemID';
    protected $fillable = [
        'bidItemProductMaterialLineItemID', 'bidItemProductLineItemID', 'productItemMaterialID', 'materialID', 'name',
        'unitID', 'unitName', 'unitAbbreviation', 'unitPrice', 'unitQuantity', 'totalQuantity', 'order',
        'createdByUserID', 'updatedByUserID', 'deletedAt', 'deletedByUserID'
    ];
}
