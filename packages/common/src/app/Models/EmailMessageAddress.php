<?php

namespace Common\Models;

use Common\Classes\DB\Model;
use Common\Traits\DB\UuidTrait;
use Illuminate\Database\Eloquent\SoftDeletes;

class EmailMessageAddress extends Model
{
    use SoftDeletes;
    use UuidTrait;

    const ADDRESS_TYPE_FROM = 1;
    const ADDRESS_TYPE_REPLY_TO = 2;
    const ADDRESS_TYPE_TO = 3;
    const ADDRESS_TYPE_CC = 4;
    const ADDRESS_TYPE_BCC = 5;

    const TYPE_EXTERNAL = 0;
    const TYPE_COMPANY_FROM = 26;
    const TYPE_COMPANY_REPLY = 27;
    const TYPE_USER = 28;
    const TYPE_CUSTOMER = 29;
    const TYPE_PROJECT_CONTACT = 30;
    const TYPE_BRAND_FROM = 31;
    const TYPE_BRAND_REPLY = 32;
    const TYPE_REGISTRATION = 33;

    protected $table = 'emailMessageAddresses';
    protected $primaryKey = 'emailMessageAddressID';
    protected $fillable = [
        'emailMessageAddressID', 'emailMessageID', 'type', 'itemID', 'addressType', 'address', 'name'
    ];
    protected $casts = [
        'type' => 'int',
        'addressType' => 'int'
    ];

    public function emailMessage()
    {
        return $this->belongsTo(EmailMessage::class, 'emailMessageID', 'emailMessageID');
    }

    public function item()
    {
        return $this->morphTo('item', 'type', 'itemID');
    }
}
