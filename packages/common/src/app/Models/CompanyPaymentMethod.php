<?php

namespace Common\Models;

use Common\Classes\DB\HistoryEntityModel;

class CompanyPaymentMethod extends HistoryEntityModel implements Interfaces\CompanyPaymentMethodInterface
{
    use Common\CompanyPaymentMethodCommon;

    protected $table = 'companyPaymentMethods';
    protected $primaryKey = 'companyPaymentMethodID';
    protected $fillable = [
        'companyID', 'itemType', 'itemID', 'name', 'paymentProfileID', 'isDefault', 'createdAt', 'createdByUserID',
        'updatedAt', 'updatedByUserID', 'deletedAt', 'deletedByUserID'
    ];
}
