<?php

namespace Common\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Evaluation extends Model
{
    const DELETED_AT = 'deletedAt';

    use SoftDeletes;

    protected $table = 'evaluation';
    protected $primaryKey = 'evaluationID';
    protected $casts = [
        'projectID' => 'int'
    ];

    public $timestamps = false;

    public static function boot()
    {
        parent::boot();
        // NOTE: force deleting is not supported
        static::deleting(function (Evaluation $evaluation) {
            // NOTE: other relationships are not deleted since they don't support soft deleting, they will need to
            // check the evaluation for deletion status
            $evaluation->appDrawings()->get()->each(function (AppDrawing $drawing) {
                $drawing->delete();
            });

            // drawings, _drawings (new drawings), photos, customEvaluation file, and evaluation directory are left
            // alone for now since these are soft deletes
        });
    }

    public function appDrawings()
    {
        return $this->hasMany(AppDrawing::class, 'evaluationID');
    }

    public function drawings()
    {
        return $this->hasMany(EvaluationDrawing::class, 'evaluationID');
    }

    public function _drawings()
    {
        return $this->hasMany(EvaluationNewDrawing::class, 'evaluationID');
    }

    public function photos()
    {
        return $this->hasMany(EvaluationPhoto::class, 'evaluationID');
    }

    public function project()
    {
        return $this->belongsTo(Project::class, 'projectID', 'projectID');
    }

    public function warranties()
    {
        return $this->hasMany(EvaluationWarranty::class, 'evaluationID');
    }

    public function isLegacy(): bool
    {
        return $this->bidItemID === null
            || optional($this->bidItem)->type === BidItem::TYPE_LEGACY;
    }

    public function scopeWithProperty($query)
    {
        $query->join('project', 'project.projectID', '=', "{$this->table}.projectID");
        $query->join('property', 'property.propertyID', '=', 'project.propertyID');
        return $query;
    }

    public function scopeWithCustomer($query)
    {
        return $this->scopeWithProperty($query)
            ->join('customer', 'customer.customerID', '=', 'property.customerID');
    }

    public function scopeWithBidItem($query)
    {
        $query->Join('customBid', 'customBid.evaluationID', '=', "{$this->table}.evaluationID");
        $query->leftJoin('bidItems', 'bidItems.bidItemID', '=', "{$this->table}.bidItemID");
        return $query;
    }


    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        return $this->scopeWithProperty($query)
            ->join('customer', 'customer.customerID', '=', 'property.customerID')
            ->where('customer.companyID', $company);
    }
}
