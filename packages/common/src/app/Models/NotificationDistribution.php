<?php

namespace Common\Models;

use Common\Classes\DB\Model;
use Common\Traits\DB\UuidTrait;
use Illuminate\Database\Eloquent\SoftDeletes;

class NotificationDistribution extends Model
{
    use SoftDeletes;
    use UuidTrait;

    const TYPE_EMAIL = 25;
    const TYPE_TEXT = 34;

    protected $table = 'notificationDistributions';
    protected $primaryKey = 'notificationDistributionID';
    protected $fillable = [
        'notificationDistributionID', 'notificationID', 'type', 'itemID'
    ];

    public function item()
    {
        return $this->morphTo('item', 'type', 'itemID');
    }

    public function notification()
    {
        return $this->belongsTo(Notification::class, 'notificationID', 'notificationID');
    }
}
