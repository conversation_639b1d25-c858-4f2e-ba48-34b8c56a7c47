<?php

namespace Common\Models;

use Common\Classes\DB\Model;
use Common\Traits\DB\UuidTrait;
use Illuminate\Database\Eloquent\SoftDeletes;

class FormItemGroupFieldProduct extends Model implements Interfaces\FormItemGroupFieldProductInterface
{
    use SoftDeletes;
    use UuidTrait;

    protected $table = 'formItemGroupFieldProducts';
    protected $primaryKey = 'formItemGroupFieldProductID';
    protected $fillable = [
        'formItemGroupFieldProductID', 'formItemGroupFieldID', 'itemType', 'itemID', 'action', 'order', 'createdByUserID',
        'updatedByUserID', 'deletedAt', 'deletedByUserID'
    ];
    protected $casts = [
        'itemType' => 'int',
        'action' => 'int',
        'order' => 'int',
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];

    public static function getActions()
    {
        return [
            static::ACTION_ADD => 'Add',
            static::ACTION_REMOVE => 'Remove'
        ];
    }

    public function item()
    {
        return $this->morphTo('item', 'itemType', 'itemID');
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy("{$this->table}.order", 'asc')->orderBy("{$this->table}.createdAt", 'asc');
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        return $query->join('formItemGroupFields', 'formItemGroupFields.formItemGroupFieldID', '=', "{$this->table}.formItemGroupFieldID")
            ->join('formItemGroups', 'formItemGroups.formItemGroupID', 'formItemGroupFields.formItemGroupID')
            ->join('formItems', 'formItems.formItemID', '=', 'formItemGroups.formItemID')
            ->leftJoin('companyFormItems', function ($join) {
                $join->where('formItems.ownerType', FormItem::OWNER_TYPE_COMPANY)
                    ->on('companyFormItems.companyFormItemID', '=', 'formItems.ownerID');
            })
            ->where(function ($query) use ($company) {
                $query->where('formItems.ownerType', FormItem::OWNER_TYPE_SYSTEM)
                    ->orWhere('companyFormItems.companyID', $company);
            });
    }
}
