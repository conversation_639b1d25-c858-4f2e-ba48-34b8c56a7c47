<?php

namespace Core\Components\Resource\Classes;

use Carbon\Carbon;
use Core\Components\Http\Classes\Http;
use Core\Exceptions\AppException;
use Core\Interfaces\JsonableInterface;
use Core\Traits\JsonableTrait;
use Illuminate\Contracts\Support\Arrayable;

/**
 * Class Entity
 *
 * @package Core\Components\Resource\Classes
 */
class Entity implements JsonableInterface, Arrayable
{
    use JsonableTrait;

    /**
     * Create instance
     *
     * @param array $data
     * @return Entity
     */
    public static function make(array $data = [])
    {
        return new static($data);
    }

    /**
     * Entity constructor
     *
     * @param array $data
     */
    public function __construct(array $data = [])
    {
        $this->setData($data);
    }

    /**
     * Add file field to entity
     *
     * This mimics a successful upload from the $_FILES superglobal
     *
     * @param string $field
     * @param string $name
     * @param string $path
     * @param array $config
     * @return $this
     * @throws AppException
     */
    public function addFile($field, $name, $path, $config = [])
    {
        if (!file_exists($path)) {
            throw new AppException('File does not exist: %s', $path);
        }
        $file = [
            'name' => $name,
            'tmp_name' => $path,
            'type' => Http::getMimeType($path),
            'size' => filesize($path),
            'error' => UPLOAD_ERR_OK
        ];
        $file = array_merge($file, $config);
        $this->set($field, $file);
        return $this;
    }

    /**
     * Get Carbon instance from entity
     *
     * @param string $key
     * @return null|Carbon
     */
    public function getDate($key)
    {
        if (($date = $this->get($key)) === null) {
            return null;
        }
        return Carbon::parse($date);
    }
}
