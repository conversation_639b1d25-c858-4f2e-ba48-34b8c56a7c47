<?php

namespace Core\Components\Resource\Classes;

use Closure;
use Core\Classes\Arr;
use Core\Components\Resource\Exceptions\ResourceException;
use Core\Components\Validation\Classes\FieldConfig;
use Core\Exceptions\AppException;
use Countable;
use Iterator;

class FieldList implements Iterator, Countable
{
    /**
     * @var Field[]
     */
    protected $fields = [];

    /**
     * @var PolyField[]
     */
    protected $poly_fields = [];

    /**
     * @var MediaField[]
     */
    protected $media_fields = [];

    /**
     * @var null|string
     */
    protected $primary_field = null;

    public static function make()
    {
        return new static;
    }

    public function __clone()
    {
        // clone all fields
        foreach ($this->fields as &$field) {
            $field = clone $field;
            $field->setList($this);
            unset($field);
        }
    }

    public function rewind()
    {
        return reset($this->fields);
    }

    public function current()
    {
        return current($this->fields);
    }

    public function key()
    {
        return key($this->fields);
    }

    public function next()
    {
        return next($this->fields);
    }

    public function valid()
    {
        return key($this->fields) !== null;
    }

    public function count()
    {
        return count($this->fields);
    }

    public function polyCount()
    {
        return count($this->poly_fields);
    }

    public function mediaCount()
    {
        return count($this->media_fields);
    }

    /**
     * @param string $name
     * @return bool
     */
    public function has($name)
    {
        return isset($this->fields[$name]);
    }

    /**
     * @param string $name
     * @return bool
     */
    public function hasPoly($name)
    {
        return isset($this->poly_fields[$name]);
    }

    /**
     * @param string $name
     * @return bool
     */
    public function hasMedia($name)
    {
        return isset($this->media_fields[$name]);
    }

    /**
     * @param string $name
     * @return Field
     * @throws ResourceException
     */
    public function get($name)
    {
        if (!$this->has($name)) {
            throw new ResourceException('Unable to find field: %s', $name);
        }
        return $this->fields[$name];
    }

    /**
     * @param string $name
     * @return PolyField
     * @throws ResourceException
     */
    public function getPoly($name)
    {
        if (!$this->hasPoly($name)) {
            throw new ResourceException('Unable to find poly field: %s', $name);
        }
        return $this->poly_fields[$name];
    }

    /**
     * @param string $name
     * @return MediaField
     * @throws ResourceException
     */
    public function getMedia($name)
    {
        if (!$this->hasMedia($name)) {
            throw new ResourceException('Unable to find media field: %s', $name);
        }
        return $this->media_fields[$name];
    }

    /**
     * @return PolyField[]
     */
    public function getAllPolyFields()
    {
        return $this->poly_fields;
    }

    public function getAllMediaFields()
    {
        return $this->media_fields;
    }

    /**
     * @param string $name
     * @param bool $primary
     * @return Field
     */
    public function field($name, $primary = false)
    {
        if ($this->has($name)) {
            return $this->fields[$name];
        }
        $field = new Field($name);
        if ($primary) {
            $field->primary();
        }
        $this->add($field, $primary);
        return $field;
    }

    /**
     * @param string $name
     * @return PolyField
     */
    public function polyField($name)
    {
        if ($this->hasPoly($name)) {
            return $this->poly_fields[$name];
        }
        $field = new PolyField($name);
        $this->addPoly($field);
        return $field;
    }

    /**
     * @param string $name
     * @return MediaField
     */
    public function mediaField($name)
    {
        if ($this->hasMedia($name)) {
            return $this->media_fields[$name];
        }
        $field = new MediaField($name);
        $this->addMedia($field);
        return $field;
    }

    /**
     * @return Field
     * @throws AppException
     * @throws ResourceException
     */
    public function primaryField()
    {
        if ($this->primary_field === null) {
            throw new AppException('No primary field defined in field list');
        }
        return $this->get($this->primary_field);
    }

    /**
     * @param Field $field
     * @param bool $primary
     * @return $this
     */
    public function add(Field $field, $primary = false)
    {
        $field->setList($this);
        $name = $field->getName();
        $this->fields[$name] = $field;
        if ($primary || $field->isPrimary()) {
            $this->primary_field = $name;
        }
        return $this;
    }

    /**
     * @param PolyField $field
     * @return $this
     */
    public function addPoly(PolyField $field)
    {
        $field->setList($this);
        $name = $field->getName();
        $this->poly_fields[$name] = $field;
        return $this;
    }

    /**
     * @param MediaField $field
     * @return $this
     */
    public function addMedia(MediaField $field)
    {
        $field->setList($this);
        $name = $field->getName();
        $this->media_fields[$name] = $field;
        return $this;
    }

    /**
     * @param string|array $field
     * @param Closure $closure
     * @return $this
     * @throws AppException
     */
    public function modify($field, Closure $closure)
    {
        if (!is_array($field)) {
            $field = [$field];
        }
        foreach ($field as $name) {
            if (!$this->has($name)) {
                throw new AppException('Unable to find field: %s', $name);
            }
            $closure($this->field($name));
            // @todo maybe re-add field if necessary
        }
        return $this;
    }

    public function getValidationFieldConfig()
    {
        $config = new FieldConfig;
        foreach ($this->fields as $field) {
            if (!$field->isEnabled()) {
                continue;
            }
            $field = $field->getValidationField();
            if ($field === null) {
                continue;
            }
            $config->add($field);
        }
        return $config;
    }

    public function ofAction($action, $access_level, array $config = [])
    {
        $list = new FieldList;
        foreach ($this->fields as $field) {
            $field = $field->ofAction($action, $config);
            if (
                (isset($config['no_passthru']) && $config['no_passthru'] && $field === null) ||
                !$field->isEnabled() ||
                !$field->hasAccess($access_level) ||
                !$field->isMutable()
            ) {
                continue;
            }
            $list->add(clone $field);
        }
        return $list;
    }

    public function ofFormat($format, $access_level, $require = false)
    {
        $list = new self;
        foreach ($this->fields as $field) {
            $field = $field->ofFormat($format, $require);
            if (($require && $field === null) || !$field->isEnabled() || !$field->hasAccess($access_level)) {
                continue;
            }
            $list->add(clone $field);
        }
        return $list;
    }

    public function only($fields)
    {
        $fields = Arr::only($this->fields, $fields);
        $list = new self;
        foreach ($fields as $field) {
            $list->add(clone $field);
        }
        return $list;
    }

    public function except($fields)
    {
        $fields = Arr::except(array_keys($this->fields), $fields);
        $list = new self;
        foreach ($fields as $field) {
            $list->add(clone $this->get($field));
        }
        return $list;
    }
}
