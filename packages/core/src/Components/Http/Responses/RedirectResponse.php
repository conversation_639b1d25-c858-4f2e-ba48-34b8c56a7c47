<?php

declare(strict_types=1);

namespace Core\Components\Http\Responses;

use Closure;
use Core\Components\Http\Classes\URI;
use Core\Components\Http\Exceptions\ResponseException;
use Core\Components\Http\Interfaces\RequestInterface;
use Core\Components\Http\Interfaces\ResponseInterface;
use Core\Components\Http\Traits\ResponseHeadersTrait;

/**
 * Class RedirectResponse
 *
 * @package Core\Components\Http\Responses
 */
class RedirectResponse implements ResponseInterface
{
    use ResponseHeadersTrait;

    /**
     * @var RequestInterface
     */
    protected RequestInterface $request;

    /**
     * @var URI
     */
    protected URI $uri;

    /**
     * RedirectResponse constructor
     *
     * @param RequestInterface $request
     */
    public function __construct(RequestInterface $request)
    {
        $this->request = $request;
        $this->uri = $request->uri();
    }

    /**
     * @param string $path
     * @param Closure|null $uri_config
     * @param int $status_code
     * @return RedirectResponse
     */
    public function to(string $path, ?Closure $uri_config = null, int $status_code = 302): self
    {
        $url = $this->uri->create(function ($uri) use ($path, $uri_config) {
            $uri->path($path);
            if ($uri_config !== null) {
                $uri_config($uri);
            }
            return $uri;
        });
        return $this->toURL($url->build(), $status_code);
    }

    /**
     * @param string $route_name
     * @param null|array $route_data
     * @param Closure|null $uri_config
     * @param int $status_code
     * @return RedirectResponse
     */
    public function toRoute(string $route_name, ?array $route_data = null, ?Closure $uri_config = null, int $status_code = 302): self
    {
        if ($route_data === null) {
            $route_data = [];
        }
        $url = $this->uri->route($route_name, $route_data, $uri_config)->build();
        return $this->toURL($url, $status_code);
    }

    /**
     * @param int $status_code
     * @return RedirectResponse
     */
    public function refresh(int $status_code = 302): self
    {
        return $this->toURL($this->uri->current()->build(), $status_code);
    }

    /**
     * @param int $status_code
     * @return RedirectResponse
     *
     * @throws ResponseException
     */
    public function back(int $status_code = 302): self
    {
        if (($url = $this->request->session()->get('last_page')) === null) {
            throw new ResponseException('Back redirect requires last page stored in session');
        }
        return $this->toURL($url, $status_code);
    }

    /**
     * @param string $url
     * @param int $status_code
     * @return $this
     */
    public function toURL(string $url, int $status_code = 302): self
    {
        $this->header('Location', $url);
        $this->statusCode($status_code);
        return $this;
    }

    public function getContent(): string
    {
        return '';
    }

    /**
     * Handle response
     */
    public function handle(): void
    {
        $this->sendHeaders();
    }
}
