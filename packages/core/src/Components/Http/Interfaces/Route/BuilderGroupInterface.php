<?php

namespace Core\Components\Http\Interfaces\Route;

/**
 * Interface BuilderInterface
 *
 * @package Core\Components\Http\Interfaces\Route
 */
interface BuilderGroupInterface
{
    /**
     * @param BuilderInterface $route
     */
    public function addRoute(BuilderInterface $route);

    /**
     * @param BuilderGroupInterface $group
     */
    public function addGroup(BuilderGroupInterface $group);

    /**
     * @param BuilderGroupInterface $group
     */
    public function group(BuilderGroupInterface $group);

    /**
     * @param string $prefix
     * @return $this
     */
    public function prefix($prefix);

    /**
     * @param string $prefix
     * @return $this
     */
    public function controllerPrefix($prefix);

    /**
     * @return BaseBuilderInterface[]
     */
    public function getBuilders();
}
