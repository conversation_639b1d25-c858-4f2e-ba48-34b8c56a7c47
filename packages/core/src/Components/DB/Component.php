<?php

declare(strict_types=1);

namespace Core\Components\DB;

use Core\Classes\Config;
use Core\Components\DB\Classes\DB;
use Core\Interfaces\DBInterface;
use Illuminate\Database\Capsule\Manager;

/**
 * Class Component
 *
 * @package Core\Components\DB
 */
class Component extends \Core\Classes\Component
{
    /**
     * Boot component
     */
    public function boot(): void
    {
        $db = new DB($this->kernel->get(Config::class), new Manager());
        $this->kernel->instance(DB::class, $db);
        $this->kernel->alias(DBInterface::class, DB::class);
    }
}
