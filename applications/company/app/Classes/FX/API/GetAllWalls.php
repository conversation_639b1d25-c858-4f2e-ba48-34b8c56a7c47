<?php

namespace App\Classes\FX\API;

use PDO;

class GetAllWalls extends Base
{
    private $results;

    public function getAllWalls()
    {
        $st = $this->db->prepare("SELECT HEX(drawingWalls.drawingID) AS drawingID, drawingWalls.nodeID, drawingWalls.x1, drawingWalls.y1, drawingWalls.x2, drawingWalls.y2, drawingWalls.lineColor, drawingWalls.createdAt, drawingWalls.updatedAt FROM drawingWalls
                                  JOIN appDrawing ON appDrawing.drawingID = drawingWalls.drawingID
                                  JOIN evaluation ON evaluation.evaluationID = appDrawing.evaluationID
                                  JOIN project ON project.projectID = evaluation.projectID
                                  JOIN customer ON customer.customerID = project.customerID
                                  JOIN user ON customer.companyID = user.companyID
                                  JOIN companies ON user.companyID = companies.companyID
                                  WHERE user.token = :token AND companies.isActive = '1' AND user.userActive = '1' ");
        $st->bindParam(":token", $this->token);
        $st->execute();

        if ($st->rowCount() >= 1) {
            while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
                $returnObject[] = $row;
            }
            $this->results = array('message' => 'success', 'walls' => $returnObject);
        } else {
            $this->results = array('message' => 'No walls found');
        }
    }

    public function getResults()
    {
        return $this->results;
    }
}
