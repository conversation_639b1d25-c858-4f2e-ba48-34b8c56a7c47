<?php

declare(strict_types=1);

namespace App\Services\Form\Components\Fields;

use App\Resources\Form\Item\Group\FieldResource;
use App\Services\Form\Classes\Structure\Group\Field;
use App\Services\Form\Components\EntryFields\ValueEntryField;
use App\Services\Form\Traits\Field\{DefaultValueTrait, InternalTrait, LengthTrait};
use Core\Components\Http\Classes\Html;
use Core\Components\Http\StaticAccessors\View;
use LightnCandy\SafeString;

/**
 * Class TextareaField
 *
 * @package App\Services\Form\Components\Fields
 */
class TextareaField extends Field
{
    use DefaultValueTrait;
    use InternalTrait;
    use LengthTrait;

    /**
     * @var int Field type
     */
    protected int $type = FieldResource::TYPE_TEXTAREA;

    /**
     * @var bool|null Determines if textarea is sized to fit the content automatically
     */
    protected ?bool $display_autosize = null;

    /**
     * Set if textarea should fit it's contents
     *
     * @param bool|null $status
     * @return $this
     */
    public function setDisplayAutosize(?bool $status = true): self
    {
        $this->display_autosize = $status;
        return $this;
    }

    /**
     * Get display autosize config item
     *
     * @return bool|null
     */
    public function getDisplayAutosize(): ?bool
    {
        return $this->display_autosize;
    }

    /**
     * Set default value, is internal, and autosize values from config
     *
     * @param array $config
     * @return $this
     */
    public function setConfig(array $config): self
    {
        $this->setDefaultValueConfig($config);
        $this->setInternalConfig($config);
        $this->setLengthConfig($config);
        $this->importFromArray([
            'display.autosize' => ['bool', 'setDisplayAutosize']
        ], $config);
        return $this;
    }

    /**
     * Build field config, add default value, is internal, and autosize if necessary
     *
     * @return array|null
     */
    protected function buildConfig(): array
    {
        $config = parent::buildConfig();
        $config = $this->getDefaultValueConfig($config);
        $config = $this->getInternalConfig($config);
        return $this->exportToArray([
            'display.autosize' => 'getDisplayAutosize'
        ], [
            'initial' => $this->getLengthConfig($config)
        ]);
    }

    /**
     * Get variables needed to build handlebars render template
     *
     * @param int $layout_type
     * @return array
     */
    protected function getTemplateVars(int $layout_type): array
    {
        $vars = parent::getTemplateVars($layout_type);
        array_unshift($vars['classes'], 't-textarea');
        $vars['value'] = View::fetch('services.form.structure.fields.value_with_default', [
            'default_value' => 'N/A' // @todo add config for this later
        ])->render();
        return $vars;
    }

    /**
     * Get the raw value of the field from an entry for use by handlebars helper
     *
     * @param ValueEntryField|null $field
     * @return SafeString|null
     */
    public function getTemplateContextValue(?ValueEntryField $field): ?SafeString
    {
        $value = null;
        if ($field !== null && ($value = $field->getValue()->getValue()) !== null) {
            $value = trim($value);
            $value = $value !== '' ? new SafeString(nl2br(Html::entityEncode($value))) : null;
        }
        return $value;
    }

    /**
     * Get handlebars context for use when building template
     *
     * @param ValueEntryField|null $field
     * @return array
     */
    public function getTemplateContext(?ValueEntryField $field): array
    {
        $value = $this->getTemplateContextValue($field);
        return [
            'has_value' => $value !== null,
            'value' => $value
        ];
    }
}
