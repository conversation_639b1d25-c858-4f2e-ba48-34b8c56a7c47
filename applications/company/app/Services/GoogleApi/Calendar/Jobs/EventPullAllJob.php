<?php

declare(strict_types=1);

namespace App\Services\GoogleApi\Calendar\Jobs;

use App\Attributes\GoogleApiJobAttribute;
use App\Services\GoogleApi\Calendar\Classes\Calendar;
use App\Services\GoogleApi\Calendar\Traits\Job\EventPullTrait;
use Common\Models\{GoogleCalendarNotificationChannelHit, GoogleCalendarPull};
use Core\Attributes\SerializeIgnoreAttribute;
use Core\Components\Queue\Classes\Job;
use Core\Components\Queue\Exceptions\JobFailedException;
use Ramsey\Uuid\UuidInterface;
use Throwable;

/**
 * Class EventPullAllJob
 *
 * @package App\Services\GoogleApi\Calendar\Jobs
 */
#[GoogleApiJobAttribute(type: 8, timeout: 300)]
class EventPullAllJob extends Job
{
    use EventPullTrait;

    /**
     * @var Calendar|null Calendar instance cache
     */
    #[SerializeIgnoreAttribute]
    protected ?Calendar $calendar = null;

    /**
     * EventPullAllJob constructor
     *
     * @param UuidInterface $calendar_id Id of calendar to pull events for
     * @param bool $full_sync Determines if a full sync is performed (full reset of local storage)
     * @param UuidInterface|null $notification_channel_hit_id ID of notification channel hit if triggered from webhook
     */
    public function __construct(
        protected UuidInterface $calendar_id,
        protected bool $full_sync = false,
        protected ?UuidInterface $notification_channel_hit_id = null
    ) {}

    /**
     * Handle import of events from external calendar into local storage
     *
     * @param Calendar $calendar
     * @throws JobFailedException
     * @throws \App\Exceptions\TimeException
     * @throws \App\Services\GoogleApi\Calendar\Exceptions\EventDeletedException
     * @throws \App\Services\GoogleApi\Calendar\Exceptions\EventDetailMismatchException
     * @throws \App\Services\GoogleApi\Exceptions\CalendarException
     * @throws \App\Services\GoogleApi\Exceptions\GoogleApiException
     */
    protected function handleImport(Calendar $calendar): void
    {
        if ($this->full_sync) {
            $calendar->clearPulledEvents();
        }

        $info = $this->importEvents($calendar, $calendar->getPullEventCursor(), $this->notification_channel_hit_id);

        if ($this->notification_channel_hit_id !== null) {
            $hit = GoogleCalendarNotificationChannelHit::find($this->notification_channel_hit_id->getBytes());
            if ($hit !== null) {
                $hit->status = GoogleCalendarNotificationChannelHit::STATUS_COMPLETED;
                $hit->save();
                // if notification channel is hit multiple times while the sync is happening, we enqueue another
                // job to pull those events. this is used as a debounce to prevent each hit causing a separate
                // pull job
                if ($hit->count > 1) {
                    static::enqueue($calendar->getID());
                }
            }
        }

        $sync_type = $info['incremental'] ? GoogleCalendarPull::TYPE_INCREMENTAL : GoogleCalendarPull::TYPE_FULL;
        $this->logPull($calendar, $sync_type, $info, $this->notification_channel_hit_id);
    }

    /**
     * Handle job
     */
    public function handle(): void
    {
        if (($this->calendar = Calendar::findByID($this->calendar_id)) === null) {
            throw new JobFailedException('Unable to find calendar with id: %s', $this->calendar_id->toString());
        }

        $this->runImport($this->calendar);
    }

    /**
     * Handle job failure
     *
     * @param Throwable $e
     * @param int $tries
     */
    public function failed(Throwable $e, int $tries): void
    {
        if ($this->calendar === null) {
            return;
        }
        $this->handleFailure($this->calendar);
    }
}
