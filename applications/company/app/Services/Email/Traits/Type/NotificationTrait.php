<?php

namespace App\Services\Email\Traits\Type;

use App\Services\Email\Classes\Message;
use App\Services\Email\Exceptions\TypeException;
use Common\Models\Notification;
use Common\Models\NotificationDistribution;
use Ramsey\Uuid\Uuid;

/**
 * Trait NotificationTrait
 *
 * @package App\Services\Email\Traits\Type
 */
trait NotificationTrait
{
    /**
     * @var null|Notification
     */
    protected $notification = null;

    /**
     * Find notification model based on payload data
     *
     * @param array $payload
     * @return Notification
     * @throws TypeException
     */
    protected function findNotification(array $payload)
    {
        if (!isset($payload['notification_id'])) {
            throw new TypeException('Notification ID not defined in payload');
        }
        $this->notification = Notification::findByUuid($payload['notification_id']);
        if ($this->notification === null) {
            throw new TypeException('Unable to find notification: %d', $payload['notification_id']);
        }
        return $this->notification;
    }

    /**
     * Get notification
     *
     * @return Notification
     * @throws TypeException
     */
    protected function getNotification()
    {
        if ($this->notification === null) {
            throw new TypeException('No notification defined');
        }
        return $this->notification;
    }

    /**
     * Get item id from notification
     *
     * @return string
     * @throws TypeException
     */
    protected function getNotificationItemID()
    {
        return $this->getNotification()->itemID;
    }

    /**
     * Save message to distributions list of notification
     *
     * @param Message $message
     * @throws TypeException
     */
    protected function saveNotificationDistribution(Message $message)
    {
        $this->getNotification()->distributions()->create([
            'notificationDistributionID' => Uuid::uuid4()->getBytes(),
            'type' => NotificationDistribution::TYPE_EMAIL,
            'itemID' => $message->getId()->getBytes()
        ]);
    }
}
