<?php

namespace App\Services\Email\Classes;

use App\Services\Email\Interfaces\TypeInterface;
use App\Services\Email\Jobs\BuildJob;
use Carbon\Carbon;
use Core\Classes\Config;
use Core\Exceptions\AppException;
use Core\Interfaces\KernelInterface;

/**
 * Class Manager
 *
 * @package App\Services\Email\Classes
 */
class Manager
{
    /**
     * List of all email types
     *
     * Format: id => full class name
     *
     * @var array
     */
    protected $types = [];

    /**
     * @var KernelInterface
     */
    protected $kernel;

    /**
     * Manager constructor
     *
     * @param Config $config
     * @param KernelInterface $kernel
     */
    public function __construct(Config $config, KernelInterface $kernel)
    {
        $this->types = $config->get('mail.types', []);
        $this->kernel = $kernel;
    }

    /**
     * Determine if type exists
     *
     * @param int $type
     * @return bool
     */
    public function hasType($type)
    {
        return isset($this->types[$type]);
    }

    /**
     * Get type by class name
     *
     * @param string $class
     * @return int
     * @throws AppException
     */
    public function getTypeByClass($class): int
    {
        if (($type = array_search($class, $this->types)) === false) {
            throw new AppException('Unable to find type for job: %s', $class);
        }
        return $type;
    }

    /**
     * Add send type by class
     *
     * Will lookup proper type based on class (determined from mail.php config file mapping) and enqueue a build job
     *
     * @param string $type_class
     * @param array $payload
     * @param Carbon|null $handle_at
     * @return void
     * @throws AppException
     */
    public function sendType($type_class, array $payload, Carbon $handle_at = null)
    {
        BuildJob::enqueue($this->getTypeByClass($type_class), $payload)->handleAt($handle_at);
    }

    /**
     * Get type instance by class name
     *
     * @param string $type_class
     * @return TypeInterface
     */
    public function getTypeInstance($type_class)
    {
        return $this->kernel->create($type_class);
    }

    /**
     * Get instance by type
     *
     * @param int $type
     * @return TypeInterface
     * @throws AppException
     */
    public function getByType($type)
    {
        if (!$this->hasType($type)) {
            throw new AppException('Unable to find type: %s', $type);
        }
        return $this->getTypeInstance($this->types[$type]);
    }
}
