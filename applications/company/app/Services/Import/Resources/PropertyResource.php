<?php

declare(strict_types=1);

namespace App\Services\Import\Resources;

use App\Services\Import\Classes\{BaseResource, Load\Processor};
use App\Services\Import\Exceptions\{RelationNotFoundException, RelationNotImportedException};
use Carbon\Carbon;
use Common\Models\Property;
use Core\Components\Validation\Classes\FieldConfig;
use Core\StaticAccessors\Config;
use Ramsey\Uuid\Uuid;

/**
 * Class PropertyResource
 *
 * @package App\Services\Import\Resources
 */
class PropertyResource extends BaseResource
{
    /**
     * @var string Display name for resource
     */
    protected string $name = 'Properties';

    /**
     * @var string App table name
     */
    protected string $table = 'property';

    /**
     * @var string Import table name
     */
    protected string $import_table = 'properties';

    /**
     * @var string Name of CSV file within zip archive
     */
    protected string $csv_file_name = 'properties.csv';

    /**
     * @var int Batch size to process at once
     */
    protected int $chunk_size = 200;

    /**
     * @inheritdoc
     */
    public function getColumns(): array
    {
        return [
            'id_user_defined' => [
                'label' => 'Id',
                'rules' => 'trim|required|max_length[100]'
            ],
            'customer_id' => [
                'label' => 'Customer Id',
                'rules' => 'trim|required|max_length[100]'
            ],
            'address_1' => [
                'label' => 'Address 1',
                'rules' => 'trim|required|max_length[100]'
            ],
            'address_2' => [
                'label' => 'Address 2',
                'rules' => 'trim|nullable|optional|max_length[100]'
            ],
            'city' => [
                'label' => 'City',
                'rules' => 'trim|required|max_length[50]'
            ],
            'state' => [
                'label' => 'State/Province',
                'rules' => 'trim|required|in_array[state_abbrs]'
            ],
            'zip' => [
                'label' => 'Postal Code',
                'rules' => 'trim|required|max_length[11]'
            ],
            'county' => [
                'label' => 'County',
                'rules' => 'trim|nullable|optional|max_length[50]'
            ],
            'township' => [
                'label' => 'Township',
                'rules' => 'trim|nullable|optional|max_length[100]'
            ],
            'latitude' => [
                'label' => 'Latitude',
                'rules' => 'trim|nullable|empty_default[0.0]|coordinate[latitude]'
            ],
            'longitude' => [
                'label' => 'Longitude',
                'rules' => 'trim|nullable|empty_default[0.0]|coordinate[longitude]'
            ],
            'created_date' => [
                'label' => 'Created Date',
                'rules' => 'trim|nullable|optional|date|to_utc'
            ]
        ];
    }

    /**
     * @inheritdoc
     */
    public function getCsvValidationFieldConfig(): FieldConfig
    {
        $config = parent::getCsvValidationFieldConfig();
        $lists = Config::get('lists');
        $config->store('state_abbrs', array_merge(array_keys($lists['states']), array_keys($lists['us_territories']), array_keys($lists['provinces'])));
        return $config;
    }

    /**
     * @inheritdoc
     */
    public function chunkQuery(object $query, Processor $processor): object
    {
        return $query->addSelect('customers.id as parent_id', 'customers.id_fx as customer_id_fx')
            ->leftJoin('customers', function ($join) use ($processor) {
                $join->on('customers.id_user_defined', '=', 'properties.customer_id')
                    ->where('customers.import_id', $processor->getImportID());
            });
    }

    /**
     * @inheritdoc
     */
    public function load(array $ids, Processor $processor): void
    {
        $properties = Property::query()
            ->select('property.*')
            ->ofCompany($processor->getCompany()['id'])
            ->whereIn('propertyID', $ids)
            ->get()
            ->keyBy('propertyID')
            ->all();
        $this->setEagerLoadedData($properties);
    }

    /**
     * @inheritdoc
     */
    public function search(object $row, Processor $processor): ?object
    {
        return Property::query()
            ->select('property.*')
            ->where('property.customerID', $row->customer_id_fx)
            ->where('property.address', $row->address_1)
            ->where('property.address2', $row->address_2)
            ->where('property.city', $row->city)
            ->where('property.state', $row->state)
            ->where('property.zip', $row->zip)
            ->first();
    }

    /**
     * Get model data from row
     *
     * @param object $row
     * @param Processor $processor
     * @return array
     * @throws RelationNotFoundException
     * @throws RelationNotImportedException
     */
    public function data(object $row, Processor $processor): array
    {
        if ($row->parent_id === null) {
            throw new RelationNotFoundException(
                'Parent not found for property %s', Uuid::fromBytes($row->id)->toString()
            );
        }
        if ($row->customer_id_fx === null) {
            throw new RelationNotImportedException(
                'Parent not imported for property %s', Uuid::fromBytes($row->id)->toString()
            );
        }
        $now = Carbon::now('UTC');
        return [
            'customerID' => $row->customer_id_fx,
            'address' => $row->address_1,
            'address2' => $row->address_2,
            'city' => $row->city,
            'state' => $row->state,
            'zip' => $row->zip,
            'county' => $row->county,
            'township' => $row->township,
            'latitude' => $row->latitude,
            'longitude' => $row->longitude,
            'createdAt' => $row->created_date ?? $now
        ];
    }

    /**
     * @inheritdoc
     */
    public function create(object $row, Processor $processor): array
    {
        $data = $this->data($row, $processor);
        $property = (new Property())->forceFill($data);
        $property->save();
        return [
            'id_fx' => $property->getKey()
        ];
    }

    /**
     * @inheritdoc
     */
    public function update(object $row, object $existing_row, Processor $processor): array
    {
        $data = $this->data($row, $processor);
        $existing_row->forceFill($data)->save();
        return [
            'id_fx' => $existing_row->getKey()
        ];
    }
}
