<?php

declare(strict_types=1);

namespace App\Services\Import\Resources\Project;

use App\Services\Import\Classes\{BaseResource, Load\Processor};
use App\Services\Import\Exceptions\{RelationNotFoundException, RelationNotImportedException};
use Common\Models\ProjectEmail;
use Ramsey\Uuid\Uuid;

/**
 * Class ContactResource
 *
 * @package App\Services\Import\Resources\Project
 */
class ContactResource extends BaseResource
{
    /**
     * @var string Display name for resource
     */
    protected string $name = 'Project Contacts';

    /**
     * @var string App table name
     */
    protected string $table = 'projectEmail';

    /**
     * @var string Import table name
     */
    protected string $import_table = 'project_contacts';

    /**
     * @var string Name of CSV file within zip archive
     */
    protected string $csv_file_name = 'project_contacts.csv';

    /**
     * @var int Batch size to process at once
     */
    protected int $chunk_size = 200;

    /**
     * @inheritdoc
     */
    public function getColumns(): array
    {
        return [
            'project_id' => [
                'label' => 'Project Id',
                'rules' => 'trim|required|max_length[100]'
            ],
            'name' => [
                'label' => 'Name',
                'rules' => 'trim|required|max_length[70]'
            ],
            'phone_number' => [
                'label' => 'Phone Number',
                'rules' => 'trim|nullable|optional|us_phone|us_phone_format'
            ],
            'email' => [
                'label' => 'Email',
                'rules' => 'trim|nullable|optional|max_length[100]|email'
            ]
        ];
    }

    /**
     * @inheritdoc
     */
    public function csvRowPostValidation(array &$row): ?array
    {
        if ($row['phone_number'] === null && $row['email'] === null) {
            return ['Phone number and/or email is required'];
        }
        return null;
    }

    /**
     * @inheritdoc
     */
    public function chunkQuery(object $query, Processor $processor): object
    {
        $import_id = $processor->getImportID();
        return $query->addSelect('projects.id as parent_id', 'projects.id_fx as project_id_fx')
            ->leftJoin('projects', function ($join) use ($import_id) {
                $join->on('projects.id_user_defined', '=', 'project_contacts.project_id')
                    ->where('projects.import_id', $import_id);
            });
    }

    /**
     * @inheritdoc
     */
    public function load(array $ids, Processor $processor): void
    {
        $project_contacts = ProjectEmail::query()
            ->select('projectEmail.*')
            ->ofCompany($processor->getCompany()['id'])
            ->whereIn('projectEmailID', $ids)
            ->get()
            ->keyBy('projectEmailID')
            ->all();
        $this->setEagerLoadedData($project_contacts);
    }

    /**
     * Get model data from row
     *
     * @param object $row
     * @param Processor $processor
     * @return array
     * @throws RelationNotFoundException
     * @throws RelationNotImportedException
     */
    public function data(object $row, Processor $processor): array
    {
        if ($row->parent_id === null) {
            throw new RelationNotFoundException(
                'Project relation not found for project contact %s', Uuid::fromBytes($row->id)->toString()
            );
        }
        if ($row->project_id_fx === null) {
            throw new RelationNotImportedException(
                'Project not imported for project contact %s', Uuid::fromBytes($row->id)->toString()
            );
        }
        return [
            'projectID' => $row->project_id_fx,
            'name' => $row->name,
            'phoneNumber' => $row->phone_number,
            'email' => $row->email
        ];
    }

    /**
     * @inheritdoc
     */
    public function create(object $row, Processor $processor): array
    {
        $data = $this->data($row, $processor);
        $data['projectEmailUUID'] = Uuid::uuid4()->getBytes();
        $contact = (new ProjectEmail())->forceFill($data);
        $contact->save();
        return [
            'id_fx' => $contact->getKey()
        ];
    }

    /**
     * @inheritdoc
     */
    public function update(object $row, object $existing_row, Processor $processor): array
    {
        $data = $this->data($row, $processor);
        $existing_row->forceFill($data)->save();
        return [
            'id_fx' => $existing_row->getKey()
        ];
    }
}
