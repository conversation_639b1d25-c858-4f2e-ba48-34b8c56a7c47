<?php

declare(strict_types=1);

namespace App\Services\Import\Resources\Project;

use App\Resources\Project\EventResource;
use App\Services\Import\Classes\{BaseResource, Load\Processor};
use App\Services\Import\Exceptions\{RelationNotFoundException, RelationNotImportedException};
use Carbon\Carbon;
use Common\Models\ProjectSchedule;
use Ramsey\Uuid\Uuid;

/**
 * Class AppointmentResource
 *
 * @package App\Services\Import\Resources\Project
 */
class AppointmentResource extends BaseResource
{
    /**
     * @var string Display name for resource
     */
    protected string $name = 'Project Appointments';

    /**
     * @var string App table name
     */
    protected string $table = 'projectSchedule';

    /**
     * @var string Import table name
     */
    protected string $import_table = 'project_appointments';

    /**
     * @var string Name of CSV file within zip archive
     */
    protected string $csv_file_name = 'project_appointments.csv';

    /**
     * @var int Batch size to process at once
     */
    protected int $chunk_size = 200;

    /**
     * @inheritdoc
     */
    public function getColumns(): array
    {
        return [
            'project_id' => [
                'label' => 'Project Id',
                'rules' => 'trim|required|max_length[100]'
            ],
            'scheduled_user_id' => [
                'label' => 'Scheduled User Id',
                'rules' => 'trim|required|max_length[100]'
            ],
            'type' => [
                'label' => 'Type',
                'rules' => [
                    'trim' => true,
                    'required' => true,
                    'in_array' => [
                        'Evaluation', 'Installation'
                    ]
                ]
            ],
            'description' => [
                'label' => 'Description',
                'rules' => 'trim|nullable|optional|max_length[5000]'
            ],
            'start_date' => [
                'label' => 'Start Date',
                'rules' => 'trim|required|date|company_date'
            ],
            'end_date' => [
                'label' => 'End Date',
                'rules' => 'trim|required|date|date_after:field[start_date]|company_date'
            ],
            'completed_date' => [
                'label' => 'Completed Date',
                'rules' => 'trim|nullable|optional|utc_date'
            ],
            'canceled_date' => [
                'label' => 'Canceled Date',
                'rules' => 'trim|nullable|optional|utc_date'
            ]
        ];
    }

    /**
     * @inheritdoc
     */
    public function chunkQuery(object $query, Processor $processor): object
    {
        $import_id = $processor->getImportID();
        return $query->addSelect(
            'projects.id as parent_id', 'projects.id_fx as project_id_fx', 'users.id_fx as scheduled_user_id_fx'
        )
            ->leftJoin('projects', function ($join) use ($import_id) {
                $join->on('projects.id_user_defined', '=', 'project_appointments.project_id')
                    ->where('projects.import_id', $import_id);
            })
            ->leftJoin('users', function ($join) use ($import_id) {
                $join->on('users.id_user_defined', '=', 'project_appointments.scheduled_user_id')
                    ->where('users.import_id', $import_id);
            });
    }

    /**
     * @inheritdoc
     */
    public function load(array $ids, Processor $processor): void
    {
        $project_appointments = ProjectSchedule::query()
            ->select('projectSchedule.*')
            ->ofCompany($processor->getCompany()['id'])
            ->whereIn('projectScheduleID', $ids)
            ->get()
            ->keyBy('projectScheduleID')
            ->all();
        $this->setEagerLoadedData($project_appointments);
    }

    /**
     * Get model data from row
     *
     * @param object $row
     * @param Processor $processor
     * @return array
     * @throws RelationNotFoundException
     * @throws RelationNotImportedException
     */
    public function data(object $row, Processor $processor): array
    {
        if ($row->parent_id === null) {
            throw new RelationNotFoundException(
                'Project relation not found for project appointment %s', Uuid::fromBytes($row->id)->toString()
            );
        }
        if ($row->project_id_fx === null) {
            throw new RelationNotImportedException(
                'Project not imported for project appointment %s', Uuid::fromBytes($row->id)->toString()
            );
        }
        if ($row->scheduled_user_id_fx === null) {
            throw new RelationNotImportedException(
                'Scheduled user not imported for project appointment %s', Uuid::fromBytes($row->id)->toString()
            );
        }
        $start_date = Carbon::parse($row->start_date, $processor->getCompany()['timezone'])->timezone('UTC');
        $status = EventResource::STATUS_ACTIVE;
        if ($row->canceled_date !== null) {
            $status = EventResource::STATUS_CANCELLED;
        } elseif ($row->completed_date !== null) {
            $status = EventResource::STATUS_COMPLETED;
        }
        return [
            'projectID' => $row->project_id_fx,
            'status' => $status,
            'scheduleType' => $row->type,
            'scheduledUserID' => $row->scheduled_user_id_fx,
            'scheduledStart' => $row->start_date,
            'scheduledEnd' => $row->end_date,
            'isSyncable' => ($start_date->gte($processor->getLoad()->storage('syncable')) ? 1 : 0),
            'description' => $row->description,
            'completedAt' => $row->completed_date,
            'cancelledAt' => $row->canceled_date,
            'importedAt' => Carbon::now('UTC')
        ];
    }

    /**
     * @inheritdoc
     */
    public function create(object $row, Processor $processor): array
    {
        $data = $this->data($row, $processor);
        $data['projectScheduleUUID'] = Uuid::uuid4()->getBytes();
        $projectSchedule = (new ProjectSchedule())->forceFill($data);
        $projectSchedule->save();
        return [
            'id_fx' => $projectSchedule->getKey()
        ];
    }

    /**
     * @inheritdoc
     */
    public function update(object $row, object $existing_row, Processor $processor): array
    {
        $data = $this->data($row, $processor);
        $existing_row->forceFill($data)->save();
        return [
            'id_fx' => $existing_row->getKey()
        ];
    }
}
