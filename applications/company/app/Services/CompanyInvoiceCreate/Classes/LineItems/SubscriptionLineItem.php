<?php

declare(strict_types=1);

namespace App\Services\CompanyInvoiceCreate\Classes\LineItems;

use App\Resources\Company\Invoice\LineItemResource;
use App\Services\CompanyInvoiceCreate\Classes\LineItem;
use Core\Components\Resource\Classes\Entity;

/**
 * Class SubscriptionLineItem
 *
 * @package App\Services\CompanyInvoiceCreate\Classes\LineItems
 */
class SubscriptionLineItem extends LineItem
{
    /**
     * @var int Line item type
     */
    protected int $type = LineItemResource::TYPE_SUBSCRIPTION;

    /**
     * @var null|int Subscription id
     */
    protected ?int $subscription_id = null;

    /**
     * Setup class data from array
     *
     * @param array $data
     * @throws \Core\Exceptions\AppException
     */
    public function hydrate(array $data): void
    {
        parent::hydrate($data);
        $this->importFromArray([
            'subscription_id' => ['int', 'setSubscriptionID']
        ], $data);
    }

    /**
     * Set subscription id
     *
     * @param int $subscription_id
     * @return $this
     */
    public function setSubscriptionID(int $subscription_id): self
    {
        $this->subscription_id = $subscription_id;
        return $this;
    }

    /**
     * Convert line item data into entity
     *
     * @return Entity
     * @throws \App\Services\CompanyInvoiceCreate\Exceptions\CompanyInvoiceCreateException
     * @throws \Core\Exceptions\AppException
     */
    public function toEntity(): Entity
    {
        $entity = parent::toEntity();
        $entity->set('subscription_id', $this->subscription_id);
        return $entity;
    }
}
