<?php

namespace App\Services\ContentTemplate;

use App\Classes\GoogleMap;
use App\Services\ContentTemplateService;
use Brick\Math\BigDecimal;
use Brick\Math\RoundingMode;
use Carbon\Carbon;
use Core\Components\Http\Classes\Html;
use LightnCandy\SafeString;
use Money\Currencies\ISOCurrencies;
use Money\Currency;
use Money\Formatter\IntlMoneyFormatter;
use Money\Parser\DecimalMoneyParser;
use NumberFormatter;
use Throwable;

class Helpers
{
    public static function partial($options)
    {
        return ContentTemplateService::renderPartial($options['hash']['name'], $options['_this']);
    }

    public static function ifEquals($value_1, $value_2, $options)
    {
        if ($value_1 === $value_2) {
            return $options['fn']();
        } else {
            return $options['inverse']();
        }
    }

    public static function formatText($text)
    {
        return new SafeString(nl2br(Html::entityEncode((string)$text)));
    }

    public static function formatDate($date, $format, $options)
    {
        try {
            $from_timezone = isset($options['hash']['from-timezone']) ? $options['hash']['from-timezone'] : null;
            $to_timezone = isset($options['hash']['to-timezone']) ? $options['hash']['to-timezone'] : null;
            $date = Carbon::parse($date, $from_timezone);
            if ($to_timezone !== null) {
                $date->tz($to_timezone);
            }
            return $date->format($format);
        } catch (Throwable $e) {
            return '';
        }
    }

    public static function formatNumber($number, $options)
    {
        try {
            $scale = null;
            if (isset($options['hash']['scale'])) {
                $scale = (int)$options['hash']['scale'];
            }
            $percent = isset($options['hash']['percent']);
            // @todo add config options
            $number = BigDecimal::of($number);
            if ($percent) {
                $number = $number->multipliedBy('100');
            }
            if ($scale !== null) {
                $number = $number->toScale($scale, RoundingMode::HALF_UP);
            }
            return (string)$number;
        } catch (Throwable $e) {
            return '';
        }
    }

    public static function formatCurrency($amount, $options)
    {
        try {
            // @todo add config options

            // round amount using shared library so it's consistent with other calculations
            $amount = (string) BigDecimal::of($amount)->toScale(2, RoundingMode::HALF_DOWN);

            $currencies = new ISOCurrencies();
            $money = (new DecimalMoneyParser($currencies))->parse($amount, new Currency('USD'));
            $formatter = new NumberFormatter('en_US', NumberFormatter::CURRENCY);
            return (new IntlMoneyFormatter($formatter, $currencies))->format($money);
        } catch (Throwable $e) {
            return '';
        }
    }

    public static function chunk($array, $options)
    {
        if (!is_array($array)) {
            return [];
        }
        $size = isset($options['hash']['size']) ? (int)$options['hash']['size'] : 1;
        return array_chunk($array, $size);
    }

    public static function googleStreetViewImageUrl($location, $options)
    {
        return GoogleMap::streetviewImageUrl($location, [
            'size' => isset($options['hash']['size']) ? $options['hash']['size'] : '400x400'
        ]);
    }

    /**
     * Conditional helper for handlebars
     * @param $v1
     * @param $operator
     * @param $v2
     * @param $options
     * @return mixed
     */
    public static function ifCond($v1, $operator, $v2, $options)
    {
        switch ($operator) {
            case '==':
                return ($v1 == $v2) ? $options['fn']() : $options['inverse']();
            case '===':
                return ($v1 === $v2) ? $options['fn']() : $options['inverse']();
            case '!=':
                return ($v1 != $v2) ? $options['fn']() : $options['inverse']();
            case '!==':
                return ($v1 !== $v2) ? $options['fn']() : $options['inverse']();
            case '<':
                return ($v1 < $v2) ? $options['fn']() : $options['inverse']();
            case '<=':
                return ($v1 <= $v2) ? $options['fn']() : $options['inverse']();
            case '>':
                return ($v1 > $v2) ? $options['fn']() : $options['inverse']();
            case '>=':
                return ($v1 >= $v2) ? $options['fn']() : $options['inverse']();
            case '&&':
                return ($v1 && $v2) ? $options['fn']() : $options['inverse']();
            case '||':
                return ($v1 || $v2) ? $options['fn']() : $options['inverse']();
            default:
                return $options['inverse']();
        }
    }

    public static function modifyDate($date, $modifier)
    {
        try {
            $date = Carbon::parse($date);
            return $date->setTimestamp(strtotime($modifier, $date->getTimestamp()))->format('c');
        } catch (Throwable $e) {
            return '';
        }
    }

    public static function math($lvalue, $operator, $rvalue)
    {
        $lvalue = (float) $lvalue;
        $rvalue = (float) $rvalue;
        return [
            '+' => $lvalue + $rvalue,
            '-' => $lvalue - $rvalue,
            '*' => $lvalue * $rvalue,
            '/' => $lvalue / $rvalue,
            '%' => $lvalue % $rvalue,
            '*%' => $lvalue * ($rvalue/100)
        ][$operator];
    }


}
