<?php

namespace App\Http\Middleware;

use Closure;
use Common\Models\Company;
use Core\Components\Auth\Classes\Auth;
use Core\Components\Http\StaticAccessors\Response;

class RedirectMiddleware
{
    protected $auth;

    public function __construct(Auth $auth)
    {
        $this->auth = $auth;
    }

    public function handle($request, Closure $next)
    {
        if ($this->auth->get('no-redirect', false)) {
            return $next($request);
        }

        $user = $this->auth->user();

        $route_name = null;
        $company_status = (int) $user->companyStatus;
        if (!in_array($company_status, [Company::STATUS_SIGNUP, Company::STATUS_TRIAL, Company::STATUS_ACTIVE])) {
            $route_name = 'page.app.company.account';
        } elseif ($company_status === Company::STATUS_SIGNUP) {
            $route_name = 'page.app.signup';
        }

        if ($route_name !== null) {
            // if not primary user, just log them out since they would be stuck in a direct loop otherwise due to the
            // fact they don't have privileges
            if (!$user->primary) {
                return Response::redirect()->toRoute('page.auth.logout');
            }
            $route = $request->route();
            if ($route === null || !in_array($route->getName(), [$route_name])) {
                return Response::redirect()->toRoute($route_name, ['path' => '']);
            }
            $this->auth->set('restricted', true);
        }

        return $next($request);
    }
}
