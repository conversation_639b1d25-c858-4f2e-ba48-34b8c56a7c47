<?php

declare(strict_types=1);

namespace App\ResourceDelegates\System\Form;

use App\Resources\System\Form\{CategoryItemResource, CategoryResource, ItemResource};
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Carbon\Carbon;
use Common\Models\SystemFormCategory;
use Core\Components\Resource\Classes\{Collection, Entity, Field, FieldList, RelationList, Request, Scope, ScopeBuilder};
use Core\Components\Resource\Exceptions\ImmutableEntityException;
use Core\Components\Resource\Requests\UpdateRequest;
use Core\Components\Validation\Classes\{FieldConfig, Rules, Validator};
use Ramsey\Uuid\UuidInterface;

class CategoryDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    public function buildRelations(RelationList $list): RelationList
    {
        $list->pivot('category_item')->resource(CategoryItemResource::class);
        $list->oneOrMany('children')->resource(CategoryResource::class);
        $list->oneOrMany('items')->resource(ItemResource::class);
        $list->oneOrMany('parent')->resource(CategoryResource::class);

        return $list;
    }

    public function buildFields(FieldList $list): FieldList
    {
        $list->field('id')
            ->primary()
            ->typeUuid()
            ->column('systemFormCategoryID')
            ->validation('Id', 'required|uuid')
            ->noSave()
            ->onAction(CategoryResource::ACTION_CREATE, function (Field $field) {
                return $field->validationRules('required|uuid|check_id')
                    ->save();
            });

        $list->field('parent_id')
            ->typeUuid()
            ->column('parentSystemFormCategoryID', true)
            ->validation('Parent Id', 'nullable|optional|uuid|check_category_id');

        $type_map = CategoryResource::getTypeMap();
        $list->field('type')
            ->validation('Type', 'required|type[int]|in_array[types]')
            ->onAction(CategoryResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric');
            })
            ->saveMutator(function ($value) use ($type_map) {
                return array_search($value, $type_map);
            })
            ->outputMutator(function ($value) use ($type_map) {
                return $type_map[$value];
            });

        $list->field('status')
            ->validation('Status', 'required|type[int]|in_array[statuses]')
            ->disableAction([CategoryResource::ACTION_CREATE, CategoryResource::ACTION_NESTED_CREATE])
            ->onAction(CategoryResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric');
            });

        $list->field('name')
            ->validation('Name', 'trim|required|max_length[200]')
            ->enableAction([CategoryResource::ACTION_SORT]);

        $list->field('order')
            ->validation('Order', 'required|type[int]')
            ->onAction([CategoryResource::ACTION_CREATE, CategoryResource::ACTION_NESTED_CREATE], function (Field $field) {
                return $field->validationRules('nullable|optional|type[int]');
            });

        $list->field('archived_at')
            ->typeDateTime()
            ->column('archivedAt')
            ->immutable();

        $list->field('archived_by_user_id')
            ->column('archivedByUserID')
            ->immutable();

        $this->timestampFields($list);

        return $list;
    }

    public function validationRules(Rules $rules, CategoryResource $resource): Rules
    {
        $rules->register('check_id', function (UuidInterface $id) use ($resource) {
            return $resource->primaryFieldInUse($id->toString()) ? 'check_id' : true;
        }, [
            'check_id' => '{label} is already in use'
        ]);

        $rules->register('check_category_id', function (UuidInterface $category_id, $params, Validator $validator) use ($resource) {
            $parent = $resource->relationResource('parent');
            if (!$parent->entityExists($category_id->toString())) {
                return 'category_not_found';
            }
            $model = $validator->getConfig()->storage('_model');
            if ($model !== null && $model->getKey() === $category_id->getBytes()) {
                return 'category_not_allowed';
            }
            return true;
        }, [
            'category_not_found' => 'Unable to find category',
            'category_not_allowed' => 'Making a category its own parent is not allowed for obvious reasons'
        ]);

        return $rules;
    }

    public function validationFieldConfig(FieldConfig $config): FieldConfig
    {
        $config->store('types', CategoryResource::getTypes());
        $config->store('statuses', CategoryResource::getStatuses());
        return $config;
    }

    public function queryScopeSearch(object $query, $term): object
    {
        return $query->searchWithRank($term);
    }

    public function anyCreateValidateAfter(Entity $entity): Entity
    {
        if (!$entity->has('order')) {
            $entity->set('order', 1);
        }
        return $entity;
    }

    public function createModelDataAfter(array $model_data, Request $request): array
    {
        $model_data['status'] = SystemFormCategory::STATUS_ACTIVE;
        return $model_data;
    }

    public function anyUpdateModelDataAfter(array $model_data, UpdateRequest $request): array
    {
        $resource = $request->resource();
        $model = $request->getModel();
        if (isset($model_data['status']) && $model_data['status'] !== $model->status) {
            switch ($model_data['status']) {
                case SystemFormCategory::STATUS_ACTIVE:
                    $model_data['archivedAt'] = null;
                    $model_data['archivedByUserID'] = null;
                    break;
                case SystemFormCategory::STATUS_ARCHIVED:
                    $model_data['archivedAt'] = Carbon::now('UTC');
                    $user = $resource->acl()->user();
                    $model_data['archivedByUserID'] = $user !== null ? $user->getKey() : null;
                    $request->store('archive_children', true);
                    break;
            }
        }
        return $model_data;
    }

    public function anyUpdateSaveAfter(UpdateRequest $request): void
    {
        if ($request->storage('archive_children', false)) {
            /** @var CategoryResource $resource */
            $resource = $request->resource();
            $category_id = $resource->getPrimaryField()->outputValueFromModel($request->getModel());
            $resource->archiveChildrenByID($category_id);
        }
    }

    public function deleteSaveAfter(Request $request): void
    {
        /** @var CategoryResource $resource */
        $resource = $request->resource();

        $category_id = $resource->getPrimaryField()->outputValueFromModel($request->getModel());
        $resource->deleteChildrenByID($category_id);

        $resource->relationResource('category_item')->deleteByCategoryID($category_id);
    }

    public function actionAllowed(int $action, CategoryResource $resource): bool
    {
        if (in_array($action, [CategoryResource::ACTION_GET_COLLECTION, CategoryResource::ACTION_GET_ENTITY])) {
            return true;
        }
        $user = $resource->acl()->user();
        if ($user === null || $user->primary) {
            return true;
        }
        return false;
    }

    public function modelIsDeletable(SystemFormCategory $model)
    {
        if ($model->children()->count() > 0) {
            throw new ImmutableEntityException('Cannot delete category which has children');
        }
        if ($model->items()->count() > 0) {
            throw new ImmutableEntityException('Cannot delete category with items assigned to it');
        }
        return true;
    }

    public function scopeBuildBefore(Scope $scope): void
    {
        $format = $scope->getFormat();
        switch ($format) {
            case 'bid-list':
                $scope->fields(['id', 'parent_id', 'name'], true);
                $scope->query(function ($query) {
                    return $query->where('type', SystemFormCategory::TYPE_BID)->active()->ordered();
                });
                break;
            case 'list-v1':
                $scope->filter('status', 'eq', CategoryResource::STATUS_ACTIVE);
                $scope->fields(['id', 'parent_id', 'name'], true);
                $scope->sort('name', 'asc');
                $scope->disablePagination();
                break;
        }
    }

    public function scopeCollection(Collection $collection, ScopeBuilder $scope_builder): Collection
    {
        if ($scope_builder->getScope()->getFormat() === 'list-v1') {
            /** @var CategoryResource $resource */
            $resource = $scope_builder->getResource();
            $collection = $resource->getNestedList($collection);
        }
        return $collection;
    }
}
