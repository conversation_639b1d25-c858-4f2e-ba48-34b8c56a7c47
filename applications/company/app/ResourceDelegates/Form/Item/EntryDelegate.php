<?php

namespace App\ResourceDelegates\Form\Item;

use App\Resources\Form\Item\Entry\Group\FieldFileResource;
use App\Resources\Form\Item\Entry\GroupResource;
use App\Resources\Form\Item\EntryResource;
use App\Resources\Company\Form\ItemResource as CompanyFormItemResource;
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Common\Models\FormItemEntry;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Field;
use Core\Components\Resource\Classes\FieldList;
use Core\Components\Resource\Classes\RelationList;
use Core\Components\Resource\Classes\Scope;
use Core\Components\Resource\Exceptions\ImmutableEntityException;
use Core\Components\Resource\Relations\CustomRelation;
use Core\Components\Resource\Relations\PolyRelation;
use Core\Components\Resource\Requests\DeleteRequest;
use Core\Components\Validation\Classes\FieldConfig;
use Core\Components\Validation\Classes\Rules;
use Core\Components\Validation\Classes\Validator;
use Ramsey\Uuid\UuidInterface;

class EntryDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    public function buildRelations(RelationList $list)
    {
        $list->oneOrMany('groups')->resource(GroupResource::class);
        $list->polymorphic('form')->types(function (PolyRelation $relation) {
            $relation->type(EntryResource::FORM_TYPE_COMPANY)->resource(CompanyFormItemResource::class);
        });
        $list->custom('images')
            ->dataCallback(function (FormItemEntry $model, Entity $entity, CustomRelation $relation) {
                $field_file_resource = FieldFileResource::make($relation->getParentScopeBuilder()->getResource()->acl());
                $images = $field_file_resource->getAllImagesByEntryID($model->getUuidKey()->toString(), $relation->getScope());
                $entity->set('images', $images);
                return $entity;
            });

        return $list;
    }

    public function buildFields(FieldList $list)
    {
        $list->field('id')
            ->primary()
            ->typeUuid()
            ->column('formItemEntryID')
            ->validation('Id', 'required|uuid')
            ->noSave()
            ->onAction([EntryResource::ACTION_CREATE, EntryResource::ACTION_NESTED_CREATE], function (Field $field) {
                return $field->validationRules('required|uuid|check_id')
                    ->save();
            });

        $form_type_map = EntryResource::getFormTypeMap();
        $list->field('form_type')
            ->column('formType', true)
            ->validation('Form Type', 'required|type[int]|in_array[form_types]')
            ->saveMutator(function ($value) use ($form_type_map) {
                return array_search($value, $form_type_map);
            })
            ->outputMutator(function ($value) use ($form_type_map) {
                return $form_type_map[$value];
            });

        $list->field('form_id')
            ->typeUuid()
            ->column('formID', true)
            ->validation('Form Id', 'required|uuid|check_form_id')
            ->onAction(EntryResource::ACTION_NESTED_CREATE, function (Field $field) {
                return $field->validationRules('required|uuid');
            });

        $list->field('is_locked')
            ->column('isLocked')
            ->validation('Is Locked', 'required|type[bool]')
            ->onAction([EntryResource::ACTION_CREATE, EntryResource::ACTION_NESTED_CREATE], function (Field $field) {
                return $field->immutable();
            });

        $this->timestampFields($list);

        return $list;
    }

    public function validationFieldConfig(FieldConfig $config)
    {
        $config->store('form_types', EntryResource::getFormTypes());
        return $config;
    }

    public function validationRules(Rules $rules, EntryResource $resource)
    {
        $rules->register('check_id', function (UuidInterface $id) use ($resource) {
            return $resource->primaryFieldInUse($id->toString()) ? 'check_id' : true;
        }, [
            'check_id' => '{label} is already in use'
        ]);

        $rules->register('check_form_id', function (UuidInterface $id, $params, Validator $validator) use ($resource) {
            if ($validator->errors()->has('form_type')) {
                return Rules::STOP;
            }
            $type = $validator->data('form_type');
            $form_resource = $resource->polyRelationResource('form', $type);

            if (!$form_resource->entityExists($id->toString())) {
                return 'check_form_id';
            }
            return true;
        }, [
            'check_form_id' => 'Unable to find form'
        ]);

        return $rules;
    }

    public function anyCreateModelDataAfter(array $model_data)
    {
        $model_data['isLocked'] = false;
        return $model_data;
    }

    public function deleteSaveBefore(DeleteRequest $request)
    {
        $resource = $request->resource();

        /** @var GroupResource $group_resource */
        $group_resource = $resource->relationResource('groups');
        $group_resource->deleteByEntryID($resource->getPrimaryField()->outputValueFromModel($request->getModel()), $request->isForced());
    }

    public function queryScopeGlobal($query, EntryResource $resource)
    {
        $user = $resource->acl()->user();
        if ($user === null) {
            return $query;
        }
        return $query->ofCompany($user->companyID);
    }

    public function actionAllowed($action, EntryResource $resource)
    {
        if (($action & EntryResource::ACTION_GROUP_READ_ONLY_FULL) > 0) {
            return true;
        }
        $user = $resource->acl()->user();
        if ($user === null || $user->primary || $user->projectManagement || $user->sales) {
            return true;
        }
        return false;
    }

    public function modelIsMutable(FormItemEntry $model)
    {
        if ($model->isLocked) {
            throw new ImmutableEntityException('Form entry is locked');
        }
        return true;
    }

    public function scopeBuildBefore(Scope $scope)
    {
        switch ($scope->getFormat()) {
            case 'form-v1':
                $scope->fields(['id', 'form_type', 'form_id'], true);
                $scope->relation('groups');
                break;
            case 'detail-v1':
                $scope->noFields();
                $scope->with(['images']);
                break;
        }
    }
}
