<?php

use App\Classes\DB\Migration;
use Illuminate\Database\Schema\Blueprint;

class AddMissingIndexesToBidMediaMigration extends Migration
{
    public function up()
    {
        $this->updateTable('bidItemMedia')
            ->indexes(function (Blueprint $table) {
                $table->index('bidItemID', 'bid_item_id_index');
                $table->index(['type', 'itemID'], 'type_item_id_index');
            })
            ->alter();
    }

    public function down()
    {
        $this->updateTable('bidItemMedia')
            ->indexes(function (Blueprint $table) {
                $table->dropIndex('bid_item_id_index');
                $table->dropIndex('type_item_id_index');
            })
            ->alter();
    }
}
