<?php
declare(strict_types=1);

use App\Classes\DB\Migration;
use Illuminate\Database\Schema\Blueprint;

final class CreateUserSettingsWithHistoryMigration extends Migration
{
    public function up()
    {
        $this->schema->rename('userSettings', 'userSettingsOld');
        $this->newTable('userSettings')
            ->primaryKey('userSettingID', false)
            ->columns(function (Blueprint $table) {
                $table->unsignedInteger('userID');
                $table->string('name', 100);
                $table->longText('value')->nullable();
            })
            ->indexes(function (Blueprint $table) {
                $table->index('userID');
                $table->index(['userID', 'name']);
            })
            ->create();
    }

    public function down()
    {
        $this->dropTables(['userSettings'], true);
        $this->schema->rename('userSettingsOld', 'userSettings');
    }
}
