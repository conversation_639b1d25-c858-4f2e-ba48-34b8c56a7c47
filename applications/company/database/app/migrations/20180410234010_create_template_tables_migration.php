<?php

use App\Classes\DB\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateTemplateTablesMigration extends Migration
{
    public function up()
    {
        // content templates
        $this->newTable('contentTemplates')
            ->primaryKey('contentTemplateID')
            ->columns(function (Blueprint $table) {
                $table->unsignedInteger('companyID');
                $table->unsignedTinyInteger('type'); // bid cover letter, bid intro, bid line items
                $table->string('name', 100);
                $table->string('alias', 100);
                $table->longText('styles')->nullable();
                $table->longText('content');
                $table->longText('scripts')->nullable();
                $table->unsignedTinyInteger('isDefault');
                $table->longText('config')->nullable();
            })
            ->indexes(function (Blueprint $table) {
                $table->index('companyID');
                $table->index(['type', 'alias']);
            })
            ->create();

        // content partials
        $this->newTable('contentPartials')
            ->primaryKey('contentPartialID')
            ->columns(function (Blueprint $table) {
                $table->unsignedInteger('companyID');
                $table->string('name', 100);
                $table->string('alias', 100);
                $table->longText('content')->nullable();
            })
            ->indexes(function (Blueprint $table) {
                $table->index('companyID');
                $table->index('alias');
            })
            ->create();
    }

    public function down()
    {
        $this->dropTables(['contentTemplates', 'contentPartials'], true);
    }
}
