<?php

use App\Classes\DB\Migration;
use App\Classes\DB\Schema\Table;
use Illuminate\Database\Schema\Blueprint;

class TextMessageMigration extends Migration
{
    public function up()
    {
        $this->newTable('textMessageServices')
            ->primaryKey('textMessageServiceID')
            ->columns(function (Blueprint $table) {
                $table->string('name', 100);
                $table->string('serviceID', 40);
            })
            ->timestamps(false, false)
            ->useHistory(false)
            ->create();

        $this->updateTable('brands')
            ->column('textMessageServiceID', Table::COLUMN_TYPE_UUID, [
                'after' => 'smtpCredentialID'
            ])
            ->useHistory(false)
            ->alter();

        $this->newTable('textMessages')
            ->primaryKey('textMessageID')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('messageType'); // inbound, outbound
                $table->unsignedTinyInteger('type'); // project event, etc.
                $table->string('body', 1600);
            })
            ->column('itemID', Table::COLUMN_TYPE_UUID, [
                'after' => 'type'
            ])
            ->indexes(function (Blueprint $table) {
                $table->index(['type', 'itemID'], 'type_item_id_index');
            })
            ->timestamps(true, false)
            ->useHistory(false)
            ->create();

        $this->newTable('textMessageNumbers')
            ->primaryKey('textMessageNumberID')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('type'); // customer, user, etc.
                $table->string('number', 16)->nullable()->comment('e.164 formatted phone number');
                $table->unsignedTinyInteger('numberType'); // from, to
            })
            ->column('textMessageID', Table::COLUMN_TYPE_UUID, [
                'after' => 'textMessageNumberID'
            ])
            ->column('itemID', Table::COLUMN_TYPE_UUID, [
                'after' => 'type',
                'nullable' => true
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('textMessageID', 'text_message_id_index');
            })
            ->timestamps(true, false)
            ->useHistory(false)
            ->create();

        $this->newTable('textMessageRecipients')
            ->primaryKey('textMessageRecipientID')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('status'); // accepted, queued, sent, delivered, undelivered, failed
                $table->string('externalMessageID', 34)->nullable();
                $table->dateTime('acceptedAt', 6)->nullable();
                $table->dateTime('queuedAt', 6)->nullable();
                $table->dateTime('sentAt', 6)->nullable();
                $table->dateTime('deliveredAt', 6)->nullable();
                $table->dateTime('undeliveredAt', 6)->nullable();
                $table->dateTime('failedAt', 6)->nullable();
                $table->dateTime('createdAt', 6);
                $table->dateTime('updatedAt', 6)->nullable();
                $table->dateTime('deletedAt', 6)->nullable();
            })
            ->column('textMessageID', Table::COLUMN_TYPE_UUID, [
                'after' => 'textMessageRecipientID'
            ])
            ->column('textMessageNumberID', Table::COLUMN_TYPE_UUID, [
                'after' => 'textMessageID'
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('textMessageID', 'text_message_id_index');
            })
            ->noTimestamps()
            ->useHistory(false)
            ->create();

        $this->newTable('textNumberSuppressions')
            ->primaryKey('textNumberSuppressionID', false)
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('source'); // user unsubscribe, invalid phone number
                $table->string('number', 16);
            })
            ->indexes(function (Blueprint $table) {
                $table->index('number', 'number_index');
            })
            ->timestamps(true, false)
            ->useHistory(false)
            ->create();

        $this->updateTable('customerPhone')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('type')->after('customerID');
            })
            ->column('customerPhoneUUID', Table::COLUMN_TYPE_UUID, [
                'after' => 'customerPhoneID'
            ])
            ->alter();

        $this->newTable('twilioWebhookLog')
            ->primaryKey('twilioWebhookLogID')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('type'); // status, incoming
                $table->longText('body');
                $table->dateTime('createdAt', 6);
            })
            ->useHistory(false)
            ->noTimestamps()
            ->create();
    }

    public function down()
    {
        $this->dropTables([
            'textMessageServices', 'textMessages', 'textMessageNumbers', 'textMessageRecipients', 'textNumberSuppressions',
            'twilioWebhookLog'
        ]);

        $this->updateTable('brands')
            ->columns(function (Blueprint $table) {
                $table->dropColumn('textMessageServiceID');
            })
            ->useHistory(false)
            ->alter();

        $this->updateTable('customerPhone')
            ->columns(function (Blueprint $table) {
                $table->dropColumn(['customerPhoneUUID', 'type']);
            })
            ->alter();
    }
}
