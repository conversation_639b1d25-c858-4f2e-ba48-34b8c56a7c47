'use strict';

const Handlebars = require('handlebars/runtime');

/**
 * Format currency helper for Handlebars
 */
function formatCurrency(amount) {
    if (!amount) return '$0.00';
    const num = parseFloat(amount);
    return '$' + num.toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    });
}

Handlebars.registerHelper('formatCurrency', formatCurrency);
