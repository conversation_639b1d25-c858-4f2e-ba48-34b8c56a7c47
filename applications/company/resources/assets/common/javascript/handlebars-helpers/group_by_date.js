'use strict';

const Handlebars = require('handlebars/runtime');

/**
 * Group by date helper for Handlebars
 */
function groupByDate(items, options) {
    if (!items || !Array.isArray(items)) return '';
    
    const grouped = {};
    items.forEach(item => {
        const date = new Date(item.created_at || item.date);
        const dateKey = date.toDateString();
        if (!grouped[dateKey]) {
            grouped[dateKey] = [];
        }
        grouped[dateKey].push(item);
    });

    let result = '';
    Object.keys(grouped).forEach(dateKey => {
        result += options.fn({
            date: dateKey,
            items: grouped[dateKey]
        });
    });
    return result;
}

Handlebars.registerHelper('groupByDate', groupByDate);
