'use strict';

const EventEmitter = require('events');

/**
 * @mixin
 */
const Event = {
    /**
     * Add event listener
     *
     * @param {string} event - Event name
     * @param {function} closure - Event handler
     */
    on(event, closure) {
        this.events.on(event, closure);
        return this;
    },

    /**
     * Remove event listener
     *
     * @param {string} event - Event name
     * @param {function} closure - Event handler
     */
    off(event, closure) {
        this.events.removeListener(event, closure);
    },

    /**
     * Emit event
     *
     * @param {string} event
     * @param {...*} data
     */
    emit(event, ...data) {
        if (this.__events === undefined) {
            return;
        }
        this.events.emit(event, ...data);
    },

    /**
     * Reset event emitter
     */
    clearEvents() {
        this.__events = undefined;
    }
};

module.exports = (object) => {
    Object.defineProperty(object.prototype, 'events', {
        get: function () {
            if (this.__events === undefined) {
                this.__events = new EventEmitter();
            }
            return this.__events;
        }
    });
    return Object.assign(object.prototype, Event);
};
