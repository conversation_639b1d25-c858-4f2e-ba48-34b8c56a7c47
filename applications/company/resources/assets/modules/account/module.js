'use strict';

window.e$ = jQuery;

const $ = require('jquery');
window.$ = window.jQuery = $;

const svgs = require.context('./resources/svg-symbols', true, /\.svg$/);
svgs.keys().forEach(svgs);

require('@ca-package/dom/src/jquery_plugin');
require('@ca-submodule/validator');
require('@ca-submodule/validator/src/validators/payment');

require('./resources/sass/main.scss');

const {layout} = require('@ca-submodule/layout');
const Account = require('./src/index');

window.Account = new Account(layout, $('.acct-wrapper'))
