<div class="row">
    <div class="small-12 medium-12 columns">
        <h4>Subscription Details</h4>
        {{#if suspended}}
        <div class="custom-callout t-yellow">
            <strong>Account Status: Suspended</strong><br>
            Your account has been suspended due to a payment failure. After settlement, you will receive an email when
            your account has been reinstated.
        </div>
        {{/if}}
        {{#if trial}}
            <div class="custom-callout t-blue">
                <strong>Account Status: Trial</strong><br>
                Your company is currently in trial. You will be automatically charged on <strong>{{trial_expires}}</strong>.
                Click <i>Subscription Options</i> below to review packages or add more users.
                <a target="_blank" href="mailto:<EMAIL>?subject=Cancel Trial">Contact us</a> before next billing to cancel your trial.
            </div>
        {{/if}}
        <h5>{{name}} Subscription</h5>
    </div>
</div>
<div class="row">
    <div class="small-12 medium-6 columns">
        <p>
            {{#if single_user}}
                Includes 1 User<br/>
            {{else}}
                {{#if users}}
                Includes {{users}} Users<br/>
                {{else}}
                Includes Unlimited Users<br/>
                {{/if}}
            {{/if}}

            {{#if single_interval}}
            <strong>{{price}}/{{interval_unit_name}}</strong>
            {{else}}
            <strong>{{price}} </strong><small>(Charged Every {{interval_length}} {{interval_unit_name}}s)</small>
            {{/if}}
            {{#if cancelled}}<br/><span class="cancelled">Subscription Cancelled</span>{{/if}}
            {{#if credit}}<br/><br/><span class="credit">Account Credit: {{credit_total}}</span>{{/if}}
        </p>
    </div>
    <div class="small-12 medium-6 columns">
        <p class="text-right">
            {{#if last_billed}}Last Billing: {{last_billed}}<br/>{{/if}}
            {{#if cancelled}}
            <strong>Subscription Ends: {{next_billed}}</strong>
            {{else}}
            Next Billing: {{next_billed}}
            {{/if}}
        </p>
    </div>
</div>
{{#if adjustments}}
<div class="row">
    <div class="small-12 medium-12 columns">
        <h5>Price Adjustments</h5>
    </div>
</div>
<div class="row">
    {{#each adjustments}}
    <div class="small-12 medium-4 columns">
        <div class="adjustment-section">
            <h6 class="adjustment-type capitalize">{{type}}</h6>
            <ul>
                {{#each items}}
                <li>
                    {{this.name}}: {{this.cost}}
                    {{#if occurrence_count}}
                        ({{occurrence_count}} {{../../interval_unit_name}}{{#ifeq occurrence_count 1}}{{else}}s{{/ifeq}})
                    {{/if}}
                    {{#if delay_count}}
                        (starts after {{delay_count}} {{../../interval_unit_name}}{{#ifeq delay_count 1}}{{else}}s{{/ifeq}})
                    {{/if}}
                </li>
                {{/each}}
            </ul>
        </div>
    </div>
    {{/each}}
</div>
{{/if}}
<div class="row">
    <div class="small-12 medium-12 columns">
        <button class="button secondary" data-change-subscription>Subscription Options</button>
        <button class="button secondary hidden" data-hide-subscription>Hide Options</button>
    </div>
</div>