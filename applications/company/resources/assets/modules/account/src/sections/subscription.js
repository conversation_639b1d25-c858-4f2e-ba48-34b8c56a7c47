'use strict';

const accounting = require('accounting');
const Decimal = require('decimal.js');
const lang = require('lodash/lang');
const moment = require('moment-timezone');

const Number = require('@cac-js/utils/number');

const Api = require('@ca-package/api');
const Table = require('@ca-submodule/table').Base;

const Section = require('../section');

const subscription_tpl = require('@cam-account-tpl/sections/subscription.hbs');
const current_subscription_tpl = require('@cam-account-tpl/sections/subscription/current_subscription.hbs');
const choose_subscription_tpl = require('@cam-account-tpl/sections/subscription/choose_subscription.hbs');
const ACHAuthorizationModal = require('../modals/subscription/ach_authorization');

require('@cac-js/handlebars-helpers/if_eq');

module.exports = class extends Section {
    constructor(account) {
        super(account);
        this._name = 'subscription';
        this.state = {
            table_loaded: false,
            table: null,
            table_scope: {
                sorts: {
                    created_at: Table.Sort.DESC
                }
            }
        };

        this.elem.items = {};
        this.modals = {};
        this.new_subscription_id = null;
        this.ach_primary_payment_method = false;
        this.ach_account = null;
        this.company_dormant = false;

        this.interval_units = {
            [Api.Constants.CompanySubscriptions.IntervalUnit.DAY]: 'day',
            [Api.Constants.CompanySubscriptions.IntervalUnit.MONTH]: 'month',
            [Api.Constants.CompanySubscriptions.IntervalUnit.YEAR]: 'year'
        };
        this.adjustment_types = {
            [Api.Constants.CompanySubscriptions.PriceAdjustments.Type.ADD_ONS]: 'add-ons',
            [Api.Constants.CompanySubscriptions.PriceAdjustments.Type.DISCOUNTS]: 'discounts',
            [Api.Constants.CompanySubscriptions.PriceAdjustments.Type.FEES]: 'fees'
        };
    };

    clear() {
        this.elem.content.empty();
    };

    intervalName(interval) {
        return this.interval_units[interval];
    };

    sortPriceAdjustments(price_adjustments_data, annual_option) {
        let adjustments = {};
        for (let adjustment in price_adjustments_data) {
            let this_adjustment = price_adjustments_data[adjustment];
            if (this.state.current_subscription !== null && this_adjustment.is_initial_subscription_only) {
                continue;
            }
            this_adjustment['show_delay'] = this.state.current_subscription === null;
            switch(this_adjustment.amount_type) {
                case Api.Constants.CompanySubscriptions.PriceAdjustments.AmountType.TOTAL:
                    this_adjustment['cost'] = annual_option
                        ? accounting.formatMoney(new Decimal(this_adjustment.amount).div(12).toFixed(2))
                        : accounting.formatMoney(this_adjustment.amount);
                    break;
                case Api.Constants.CompanySubscriptions.PriceAdjustments.AmountType.PERCENTAGE:
                    this_adjustment['cost'] = this_adjustment.amount+'%';
                    break;
            }

            if (lang.isUndefined(adjustments[this.adjustment_types[this_adjustment.type]])) {
                adjustments[this.adjustment_types[this_adjustment.type]] = [
                    this_adjustment
                ];
            } else {
                adjustments[this.adjustment_types[this_adjustment.type]].push(this_adjustment);
            }
        }
        let _adjustments = [];
        for (let type of Object.values(this.adjustment_types)) {
            if (!lang.isUndefined(adjustments[type])) {
                _adjustments.push({
                    type: type,
                    items: adjustments[type]
                });
            }
        }
        return _adjustments;
    };

    calculateTotalDueToday(price, adjustments) {
        let total_price_today = new Decimal(price);
        for (let type of adjustments) {
            let price_now = total_price_today;
            for (let adjustment of type.items) {
                if (adjustment.delay_count > 0) {
                    continue;
                }
                let adjustment_amount = adjustment.amount;
                if (adjustment.amount_type === Api.Constants.CompanySubscriptions.PriceAdjustments.AmountType.PERCENTAGE) {
                    adjustment_amount = new Decimal(adjustment.amount).div(100).mul(price_now);
                }
                switch (type.type) {
                    case 'add-ons':
                    case 'fees':
                        total_price_today = Decimal.add(total_price_today, adjustment_amount);
                        break;
                    case 'discounts':
                        total_price_today = Decimal.sub(total_price_today, adjustment_amount);
                        break;
                }
            }
        }
        return accounting.formatMoney(new Decimal(total_price_today).toFixed(2));
    };

    submitRegistrationChange() {
        this.fire('show-loader');
        this.elem.error.removeClass('show');

        if (this.state.data.current_subscription) {
            Api.Resources.Companies().data({
                'subscription_option_id': this.new_subscription_id
            }).method(Api.Request.Method.PUT).custom('current/modify-subscription').then((result, response) => {
                this.fire('hide-loader');
                if (this.company_dormant) {
                    location.reload();
                } else {
                    this.clear();
                    this.load(true);
                }
            }, (error) => {
                this.fire('hide-loader');
                this.modals.error.setError(error);
                this.modals.error.open();
                reject();
            });
        } else {
            Api.Resources.Companies().data({
                'subscription_option_id': this.new_subscription_id
            }).method(Api.Request.Method.POST).custom('current/create-subscription').then((result, response) => {
                this.fire('hide-loader');
                if (this.company_dormant) {
                    location.reload();
                } else {
                    this.clear();
                    this.load(true);
                }
            }, (error) => {
                this.fire('hide-loader');
                this.modals.error.setError(error);
                this.modals.error.open();
                reject();
            });
        }
    };

    newSubscriptionOptions(config) {
        let current_subscription = null;
        if (!lang.isUndefined(config.current_subscription)) {
            current_subscription = config.current_subscription;
        }
        return new Promise((resolve, reject) => {
            Api.Resources.Companies().relation('price_adjustments').method(Api.Request.Method.GET)
                .custom('current/subscription-options')
                .then((result, response) => {
                    this.new_subscription_id = null;

                    let option_types = {
                        [Api.Constants.CompanySubscriptions.IntervalUnit.MONTH]: [],
                        [Api.Constants.CompanySubscriptions.IntervalUnit.YEAR]: []
                    };
                    result.collection.filter((option) => {
                        option_types[option.interval_unit].push(option);
                    });

                    for (let entity in result.collection) {
                        let this_entity = result.collection[entity];
                        let option_count = option_types[this_entity.interval_unit].length;
                        let adjustments = this.sortPriceAdjustments(this_entity.price_adjustments, this_entity.interval_unit === Api.Constants.CompanySubscriptions.IntervalUnit.YEAR);
                        this_entity['columns'] = option_count === 1 ? 6 : Math.floor(12/option_count);
                        this_entity['current'] = this_entity.id === current_subscription;
                        this_entity['hidden'] = this_entity.interval_unit === Api.Constants.CompanySubscriptions.IntervalUnit.YEAR;
                        this_entity['disabled_option'] = account_data.user_count > this_entity.users && this_entity.users !== null;
                        this_entity['single_user'] = this_entity.users === 1;
                        this_entity['single_interval'] = this_entity.interval_length === 1;
                        this_entity['interval_unit_name'] = this.intervalName(this_entity.interval_unit);
                        this_entity['adjustments'] = adjustments;
                        this_entity['price'] = accounting.formatMoney(this_entity.price);
                        this_entity['price_today'] = this.calculateTotalDueToday(this_entity.cost, adjustments);
                        this_entity['annual'] = this_entity.interval_unit === Api.Constants.CompanySubscriptions.IntervalUnit.YEAR;
                        delete this_entity.price_adjustments;
                    }
                    let content = $(choose_subscription_tpl({
                        inactive: config.dormant,
                        trial: config.trial,
                        trial_expires: config.trial_expires,
                        subscription_options: result.collection
                    }));

                    this.elem.no_payment_method = content.find('[data-js="no-payment-method"]').hide();
                    this.elem.save_subscription = content.find('.button.save');
                    this.elem.subscription_options = content.find('.subscription-option');
                    this.elem.button_group = content.find('.button-group');
                    this.elem.error = content.find('.error');
                    this.elem.cost_summary = content.find('.cost-summary');
                    this.company_dormant = config.dormant;

                    if (!this.state.data.default_payment_method) {
                        this.elem.no_payment_method.show();
                        this.elem.save_subscription.prop('disabled', true);
                    }

                    this.elem.subscription_options.on('click.fx', (e) => {
                        let this_item = $(e.currentTarget);
                        if (!this_item.attr('disabled')) {
                            this.elem.subscription_options.removeClass('selected');
                            this_item.addClass('selected');
                            this.new_subscription_id = this_item.data('id');
                            this.elem.cost_summary.find('.callout .price').text(this_item.data('price'));
                            this.elem.cost_summary.show();
                        }
                    });
                    this.elem.button_group.on('click.fx', 'a', (e) => {
                        var this_item = $(e.currentTarget);
                        if (!this_item.hasClass('active')) {
                            let id = this_item.data('button-id');
                            this.elem.subscription_options.each(function() {
                                let this_option = $(this);
                                if (this_option.data('unit') === id) {
                                    this_option.parent().removeClass('hidden');
                                } else {
                                    this_option.parent().addClass('hidden');
                                }
                            });
                            this.elem.button_group.find('a').removeClass('active');
                            this_item.addClass('active');
                        }
                    });
                    this.elem.save_subscription.on('click.fx', (e) => {
                        e.preventDefault();
                        if (this.new_subscription_id !== null) {
                            if (this.ach_primary_payment_method) {
                                let selected_subscription = this.elem.root.find(`.subscription-option[data-id="${this.new_subscription_id}"]`);
                                this.modals.ach_authorization.open({
                                    name: userInfo.name,
                                    number: this.ach_account,
                                    date: moment().format('M/D/YYYY'),
                                    total: `${accounting.formatMoney(selected_subscription.data('price'))}`
                                });
                            } else {
                                this.submitRegistrationChange();
                            }
                        } else {
                            this.elem.error.addClass('show');
                        }
                        return false;
                    });
                    resolve(content);
                }, (error) => {
                    this.modals.error.setError(error);
                    this.modals.error.open();
                    reject();
                });
        });
    };

    populate(data) {
        this.state.company_status = data.status;
        this.state.current_subscription = data.current_subscription;

        let credit_total = 0;
        if (data.credits.length > 0) {
            for (let credit in data.credits) {
                let this_credit = data.credits[credit];
                if (this_credit.is_expended) {
                    continue;
                }
                credit_total = parseFloat(this_credit.remaining_amount) + credit_total;
            }
        }

        if (data.status === Api.Constants.Company.Status.TRIAL && data.current_subscription !== null) {
            data.status = Api.Constants.Company.Status.ACTIVE;
        }
        switch(data.status) {
            case Api.Constants.Company.Status.ACTIVE:
            case Api.Constants.Company.Status.SUSPENDED:
                let adjustments = this.sortPriceAdjustments(data.current_subscription.price_adjustments, false);

                let content = $(current_subscription_tpl({
                    id: data.current_subscription.id,
                    status: data.status,
                    name: data.current_subscription.name,
                    single_user: data.current_subscription.users === 1,
                    users: data.current_subscription.users,
                    price: accounting.formatMoney(data.current_subscription.price),
                    single_interval: data.current_subscription.interval_length === 1,
                    interval_length: data.current_subscription.interval_length,
                    interval_unit_name: this.intervalName(data.current_subscription.interval_unit),
                    cancelled: data.current_subscription.status === Api.Constants.CompanySubscriptions.Status.CANCELLED,
                    last_billed: data.current_subscription.last_billed_at === null ? '' : moment(data.current_subscription.last_billed_at).format('M/D/YYYY'),
                    next_billed: moment(data.current_subscription.next_bill_at).format('M/D/YYYY'),
                    adjustments: adjustments,
                    billing_cycles: data.current_subscription.billing_cycles,
                    suspended: data.status === Api.Constants.Company.Status.SUSPENDED,
                    trial: data.status === Api.Constants.Company.Status.TRIAL,
                    trial_expires: moment(data.trial_expires_at).utc().format('MMMM D, YYYY'),
                    credit: credit_total > 0,
                    credit_total: accounting.formatMoney(credit_total)
                }));
                let change_subscription = content.find('[data-change-subscription]');
                let hide_subscription = content.find('[data-hide-subscription]');
                let loaded_subscriptions = false;
                let subscription_options = '';

                let default_payment_method = data.default_payment_method;
                this.ach_primary_payment_method = default_payment_method.item_type === Api.Constants.CompanyPaymentMethods.Type.ACH;
                if (this.ach_primary_payment_method) {
                    this.ach_account = default_payment_method.item.account_number;
                }

                change_subscription.on('click.fx', () => {
                    if (!loaded_subscriptions) {
                        this.fire('show-loader');
                        let config = {
                            dormant: false,
                            current_subscription: data.current_subscription.subscription_option_id
                        };
                        this.newSubscriptionOptions(config).then((result) => {
                            change_subscription.addClass('hidden');
                            hide_subscription.removeClass('hidden');
                            loaded_subscriptions = true;
                            subscription_options = result;
                            this.elem.content.append(result);
                            this.fire('hide-loader');
                        });
                    } else {
                        change_subscription.addClass('hidden');
                        hide_subscription.removeClass('hidden');
                        subscription_options.show();
                    }
                });
                hide_subscription.on('click.fx', () => {
                    hide_subscription.addClass('hidden');
                    change_subscription.removeClass('hidden');
                    this.elem.error.removeClass('show');
                    subscription_options.hide();
                    this.elem.cost_summary.hide();
                    this.elem.subscription_options.removeClass('selected');
                });

                this.elem.content.append(content);
                break;
            case Api.Constants.Company.Status.TRIAL:
            case Api.Constants.Company.Status.DORMANT:
                let config = {
                    dormant: data.status === Api.Constants.Company.Status.DORMANT,
                    trial: data.status === Api.Constants.Company.Status.TRIAL,
                    trial_expires: moment(data.trial_expires_at).utc().format('MMMM D, YYYY')
                };
                this.newSubscriptionOptions(config).then((result) => {
                    this.elem.content.append(result);
                });
                break;
        }
    };

    /**
     * Create the DataTable and apply settings and defaults
     */
    createTable() {
        let type_map = new Map([
            [Api.Constants.CompanyInvoices.Type.GENERAL, 'General'],
            [Api.Constants.CompanyInvoices.Type.SUBSCRIPTION, 'Subscription']
        ]);
        let status_map = new Map([
            [Api.Constants.CompanyInvoices.Status.READY, '<span class="h-text t-blue">Open</span>'],
            [Api.Constants.CompanyInvoices.Status.PENDING, '<span class="h-text t-yellow">Pending</span>'],
            [Api.Constants.CompanyInvoices.Status.PAID, '<span class="h-text t-green">Paid</span>'],
            [Api.Constants.CompanyInvoices.Status.FAILED, '<span class="h-text t-red">Failed</span>'],
            [Api.Constants.CompanyInvoices.Status.REFUNDING, '<span class="h-text t-grey">Refunding</span>'],
            [Api.Constants.CompanyInvoices.Status.REFUNDED, '<span class="h-text t-grey">Refunded</span>'],
            [Api.Constants.CompanyInvoices.Status.VOIDING, '<span class="h-text t-grey">Voiding</span>'],
            [Api.Constants.CompanyInvoices.Status.VOIDED, '<span class="h-text t-grey">Voided</span>']
        ]);

        this.state.table = new Table(this.elem.invoice_table)
            .on('row_click', (data) => {
                window.open(data.statement_file_media_urls.original, '_blank');
            })
            .on('row_drawn', (row, data) => {
                if (data.status === Api.Constants.CompanyInvoices.Status.FAILED) {
                    this.elem.failed_payment.removeClass('t-hidden');
                }
            });

        this.state.table.setHeader({
            name: 'Invoices',
            custom_search: false,
            search: false
        });

        // set toolbar config
        this.state.table.setToolbar({
            filter: false,
            settings: false
        });

        // set columns config
        this.state.table.setColumns({
            type: {
                label: 'Type',
                responsive: 4,
                width: '15%',
                value: (data, type) => type === 'display' ? type_map.get(data.type) : data.type,
            },
            description: {
                label: 'Description',
                responsive: 1,
                width: '30%'
            },
            total: {
                label: 'Total',
                value: data => Number.toCurrency(data.total),
                responsive: 2,
                width: '15%'
            },
            created_at: {
                label: 'Created',
                value: (data) => {
                    let date = data.created_at;
                    if (date === null) {
                        return '';
                    }
                    return moment(date).tz(this.account.layout.user.timezone).format('MM/DD/YYYY');
                },
                width: '15%'
            },
            status: {
                label: 'Status',
                width: '10%',
                value: (data, type) => type === 'display' ? status_map.get(data.status) : data.status,
                orderable: false
            }
        });

        // set row action config
        this.state.table.setRowActions({
            pay: {
                label: 'Pay',
                visible: (data) => {
                    return data.status === Api.Constants.CompanyInvoices.Status.READY ||
                        (
                            data.status === Api.Constants.CompanyInvoices.Status.FAILED &&
                            data.type === Api.Constants.CompanyInvoices.Type.GENERAL
                        );
                },
                action: (data) => {
                    Api.Resources.CompanyInvoices().method(Api.Request.Method.PUT)
                        .custom(`${data.id}/pay`)
                        .then(() => {
                            this.clear();
                            this.load(true);
                        }, (error) => {
                            this.modals.error.setError(error);
                            this.modals.error.open();
                        });
                }
            },
            settle: {
                label: 'Settle',
                visible: (data) => {
                    return data.status === Api.Constants.CompanyInvoices.Status.FAILED &&
                    data.type === Api.Constants.CompanyInvoices.Type.SUBSCRIPTION;
                },
                action: () => {
                    Api.Resources.CompanySubscriptions().method(Api.Request.Method.PUT)
                        .custom('current/settle')
                        .then(() => {
                            this.clear();
                            this.load(true);
                        }, (error) => {
                            this.modals.error.setError(error);
                            this.modals.error.open();
                        });
                }
            },
            download: {
                label: 'Open PDF',
                link: {
                    href: data => data.statement_file_media_urls.original,
                    target: '_blank'
                }
            }
        });

        this.state.table.setAjax(Api.Resources.CompanyInvoices, (request) => {
            request.accept('application/vnd.adg.fx.account-v1+json');
        });

        // modify table state
        if (this.state.table_scope) {
            this.state.table.setState(this.state.table_scope);
        }
    };

    async load(reload) {
        try {
            let {data} = await Api.Resources.Companies().accept('application/vnd.adg.fx.account-v1+json')
                .retrieve('current');
            this.state.data = data;
            this.populate(data);
            if (!reload) {
                this.fire('load');
            }
            if (this.state.table_loaded) {
                this.state.table.draw(false);
            } else {
                this.state.table.setState(this.state.table_scope);
                this.state.table.build();
                this.state.table_loaded = true;
            }
        } catch (e) {
            console.log(e);
            this.modals.error.setError(e);
            this.modals.error.open();
        }
    };

    unload() {
        this.clear();
    };

    boot() {
        super.boot();

        this.elem.content = this.elem.root.find('[data-content]');
        this.elem.invoice_table = this.elem.root.fxFind('invoices');
        this.elem.failed_payment = this.elem.root.fxFind('failed-payment');
        this.modals.ach_authorization = new ACHAuthorizationModal();
        this.createTable();

        this.modals.ach_authorization.on('confirm', () => {
            this.submitRegistrationChange();
        });

        this.fire('booted');
    };

    render() {
        return subscription_tpl({
            company_id: account_data.id
        });
    };
};
