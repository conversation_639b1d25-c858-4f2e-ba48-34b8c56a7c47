'use strict';

const Modal = require('@ca-package/router/src/modal');
const {createSuccessMessage} = require('@cas-notification-toast-js/message/success');

class Remove extends Modal {
    /**
     * Get and cache modal
     *
     * @returns {module:Modal.Confirm}
     */
    get modal() {
        if (this.state.modal === undefined) {
            let modal = require('../../../modals/integrations/calendar_remove');
            this.state.modal = new modal(this);
        }
        return this.state.modal;
    };

    /**
     * Open remove modal with promise
     *
     * @param {string} calendar_id
     * @returns {Promise<unknown>}
     */
    openModal(calendar_id) {
        return new Promise((resolve, reject) => {
            return this.modal.open({
                calendar_id,
                promise: {resolve, reject}
            });
        });
    };

    /**
     * Load modal
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async load(request, next) {
        await super.load(request, next);
        this.openModal(request.params.calendar_id).then(result => {
            let query = null;
            if (result !== null) {
                this.router.main_route.layout.toasts.addMessage(createSuccessMessage('Calendar has been successfully removed'));
                query = {update: 'true'};
            }
            this.router.navigate('integrations', {}, query);
        });
    };

    /**
     * Unload modal
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async unload(request, next) {
        this.modal.externalClose();
        await super.unload(request, next);
    };
}

module.exports = Remove;
