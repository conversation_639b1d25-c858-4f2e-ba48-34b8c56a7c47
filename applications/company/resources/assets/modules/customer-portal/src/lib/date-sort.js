'use strict';

const {findChild, jsSelector} = require("@ca-package/dom");

/**
 * Date sorting utility for customer portal pages
 */
export class DateSort {
    /**
     * Constructor
     * @param {jQuery} container - The container element
     * @param {Function} renderCallback - Callback to re-render content after sorting
     */
    constructor(container, renderCallback) {
        this.container = container;
        this.renderCallback = renderCallback;
        this.currentOrder = 'newest'; // 'newest' or 'oldest'
        this.toggleButton = null;
        
        this.setupToggleButton();
    }

    /**
     * Setup the toggle button event handler
     */
    setupToggleButton() {
        if (this.container) {
            this.toggleButton = findChild(this.container, jsSelector('date-sort-toggle'));
            
            if (this.toggleButton && this.toggleButton.length > 0) {
                this.toggleButton.off('click.date-sort').on('click.date-sort', (e) => {
                    e.preventDefault();
                    this.toggleSortOrder();
                });
            }
        }
    }

    /**
     * Toggle between newest first and oldest first
     */
    toggleSortOrder() {
        this.currentOrder = this.currentOrder === 'newest' ? 'oldest' : 'newest';
        this.updateButtonState();
        
        if (this.renderCallback) {
            this.renderCallback();
        }
    }

    /**
     * Update the button visual state
     */
    updateButtonState() {
        if (this.toggleButton && this.toggleButton.length > 0) {
            const button = this.toggleButton[0];
            const textElement = button.querySelector('.c-dst-text');
            
            if (this.currentOrder === 'newest') {
                button.setAttribute('data-sort-order', 'newest');
                if (textElement) textElement.textContent = 'Newest First';
                button.setAttribute('aria-label', 'Sort by newest first');
            } else {
                button.setAttribute('data-sort-order', 'oldest');
                if (textElement) textElement.textContent = 'Oldest First';
                button.setAttribute('aria-label', 'Sort by oldest first');
            }
        }
    }

    /**
     * Sort appointments by date
     * @param {Array} appointments - Array of appointment objects
     * @returns {Array} Sorted appointments
     */
    sortAppointments(appointments) {
        if (!appointments || !Array.isArray(appointments)) return [];
        
        return [...appointments].sort((a, b) => {
            const dateA = new Date(a.scheduledDate || a.date);
            const dateB = new Date(b.scheduledDate || b.date);
            
            return this.currentOrder === 'newest' ? dateB - dateA : dateA - dateB;
        });
    }

    /**
     * Sort bids by date
     * @param {Array} bids - Array of bid objects
     * @returns {Array} Sorted bids
     */
    sortBids(bids) {
        if (!bids || !Array.isArray(bids)) return [];
        
        return [...bids].sort((a, b) => {
            const dateA = new Date(a.status_summary?.date || a.createdAt || a.date);
            const dateB = new Date(b.status_summary?.date || b.createdAt || b.date);
            
            return this.currentOrder === 'newest' ? dateB - dateA : dateA - dateB;
        });
    }

    /**
     * Sort invoices by date
     * @param {Array} invoices - Array of invoice objects
     * @returns {Array} Sorted invoices
     */
    sortInvoices(invoices) {
        if (!invoices || !Array.isArray(invoices)) return [];
        
        return [...invoices].sort((a, b) => {
            const dateA = new Date(a.bidAccepted || a.bidFirstSent || a.createdAt || a.date);
            const dateB = new Date(b.bidAccepted || b.bidFirstSent || b.createdAt || b.date);
            
            return this.currentOrder === 'newest' ? dateB - dateA : dateA - dateB;
        });
    }

    /**
     * Sort uploads by date
     * @param {Array} uploads - Array of upload objects
     * @returns {Array} Sorted uploads
     */
    sortUploads(uploads) {
        if (!uploads || !Array.isArray(uploads)) return [];
        
        return [...uploads].sort((a, b) => {
            const dateA = new Date(a.createdAt || a.date);
            const dateB = new Date(b.createdAt || b.date);
            
            return this.currentOrder === 'newest' ? dateB - dateA : dateA - dateB;
        });
    }

    /**
     * Get current sort order
     * @returns {string} Current sort order ('newest' or 'oldest')
     */
    getCurrentOrder() {
        return this.currentOrder;
    }

    /**
     * Cleanup event handlers
     */
    destroy() {
        if (this.toggleButton) {
            this.toggleButton.off('click.date-sort');
        }
    }
}
