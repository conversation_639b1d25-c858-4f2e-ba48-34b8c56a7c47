/**
 * Format date for display
 * @param {string} dateString - ISO date string
 * @returns {string} Formatted date
 */
function formatDate(dateString) {
    if (!dateString) return '';

    const date = new Date(dateString);
    const options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    };

    return date.toLocaleDateString('en-US', options);
}

/**
 * Format time for display
 * @param {string} dateString - ISO date string
 * @returns {string} Formatted time
 */
function formatTime(dateString) {
    if (!dateString) return '';

    const date = new Date(dateString);
    const options = {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    };

    return date.toLocaleTimeString('en-US', options);
}

/**
 * Format currency for display
 * @param {number|string} amount - Amount to format
 * @returns {string} Formatted currency
 */
function formatCurrency(amount) {
    if (!amount && amount !== 0) return '$0.00';

    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(numAmount);
}

/**
 * Format date for bid sent display
 * @param {string} dateString - ISO date string
 * @returns {string} Formatted date like "September 13th 2025"
 */
function formatBidDate(dateString) {
    if (!dateString) return '';

    const date = new Date(dateString);
    const options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    };

    const formatted = date.toLocaleDateString('en-US', options);
    // Add ordinal suffix to day
    const day = date.getDate();
    const suffix = getOrdinalSuffix(day);
    return formatted.replace(day.toString(), day + suffix);
}

/**
 * Get ordinal suffix for a number
 * @param {number} num - Number to get suffix for
 * @returns {string} Ordinal suffix
 */
function getOrdinalSuffix(num) {
    const j = num % 10;
    const k = num % 100;
    if (j === 1 && k !== 11) return 'st';
    if (j === 2 && k !== 12) return 'nd';
    if (j === 3 && k !== 13) return 'rd';
    return 'th';
}

/**
 * Group uploads by date
 * @param {Array} uploads - Array of upload objects
 * @returns {Array} Array of date groups with images
 */
function groupByDate(uploads) {
    if (!uploads || !Array.isArray(uploads)) return [];

    const groups = {};

    uploads.forEach((upload, index) => {
        if (!upload || !upload.createdAt) return;

        try {
            const date = new Date(upload.createdAt);

            // Check if date is valid
            if (isNaN(date.getTime())) {
                console.warn('Invalid date for upload:', upload);
                return;
            }

            const baseDateString = date.toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
            const day = date.getDate();
            const suffix = getOrdinalSuffix(day);
            const dateKey = baseDateString.replace(day.toString(), day + suffix);

            if (!groups[dateKey]) {
                groups[dateKey] = {
                    date: dateKey,
                    images: []
                };
            }

            // Add 1-based index for display
            groups[dateKey].images.push({
                ...upload,
                displayIndex: index + 1
            });
        } catch (error) {
            console.warn('Error processing upload date:', upload, error);
        }
    });

    // Sort groups by date (most recent first)
    return Object.values(groups).sort((a, b) => {
        const dateA = new Date(a.images[0]?.createdAt);
        const dateB = new Date(b.images[0]?.createdAt);
        return dateB - dateA;
    });
}

/**
 * Group appointments by month
 * @param {Array} appointments - Array of appointment objects
 * @param {string} sortOrder - Sort order ('newest' or 'oldest')
 * @returns {Array} Array of month groups with appointments
 */
function groupByMonth(appointments, sortOrder = 'newest') {
    if (!appointments || !Array.isArray(appointments)) return [];

    const groups = {};

    appointments.forEach((appointment) => {
        if (!appointment || !appointment.scheduledStart) return;

        try {
            const date = new Date(appointment.scheduledStart);

            // Check if date is valid
            if (isNaN(date.getTime())) {
                console.warn('Invalid date for appointment:', appointment);
                return;
            }

            const monthKey = date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long'
            });

            if (!groups[monthKey]) {
                groups[monthKey] = {
                    month: monthKey,
                    appointments: [],
                    sortDate: new Date(date.getFullYear(), date.getMonth(), 1) // First day of month for sorting
                };
            }

            groups[monthKey].appointments.push(appointment);
        } catch (error) {
            console.warn('Error processing appointment date:', appointment, error);
        }
    });

    return Object.values(groups).sort((a, b) => {
        return sortOrder === 'newest' ? b.sortDate - a.sortDate : a.sortDate - b.sortDate;
    });
}


/**
 * Format file size for display
 * @param {number} bytes - File size in bytes
 * @returns {string} Formatted file size
 */
function formatFileSize(bytes) {
    if (!bytes || bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}
/**
 * Handlebars helper to compare two values for equality
 * @param {any} value_1 - First value
 * @param {any} value_2 - Second value
 * @param {object} options - Handlebars options
 * @returns {string} Handlebars block
 */
function ifEq(value_1, value_2, options) {
    return value_1 === value_2 ? options.fn(this) : options.inverse(this);
}

export {
    formatDate,
    formatTime,
    formatCurrency,
    formatBidDate,
    getOrdinalSuffix,
    groupByDate,
    groupByMonth,
    formatFileSize,
    ifEq
};
