/**
 * @module Customer Portal
 */

'use strict';

import Router from '@ca-package/router';

import {MainPage} from './pages/main';

/**
 * Main controller for Customer Portal
 *
 * @memberof module:Customer Portal
 */
export class Controller {
    /**
     * Customer Portal constructor
     *
     * @param {jQuery} root - jQuery element that contains the customer portal module
     *
     */
    constructor(root) {
        this.elem = {root};
        this.state = {};
        this.boot();
    };





    /**
     * Get router instance
     *
     * @readonly
     *
     * @returns {module:Router.Controller}
     */
    get router() {
        return this.state.router;
    };

    /**
     * Boot module
     */
    boot() {
        // Extract the customer UUID from the current URL path
        const currentPath = window.location.pathname;
        const customerPortalMatch = currentPath.match(/^\/customer-portal\/([a-f0-9\-]{36})/);

        let basePath = '/customer-portal';
        if (customerPortalMatch) {
            // Include the UUID in the base path to preserve it in the URL
            basePath = `/customer-portal/${customerPortalMatch[1]}`;
        }

        this.state.router = new Router(MainPage, {
            base_path: basePath
        });
        this.state.router.boot(this.elem.root);
    };
}
