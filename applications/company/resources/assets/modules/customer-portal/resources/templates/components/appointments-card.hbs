<!-- Unified appointments card component -->
<div class="c-card">
    <header class="c-card-header">
        <h3>Appointments</h3>
        <div class="c-ch-actions">
            {{#unless showSeeAll}}
                {{> partials/date-sort-toggle}}
            {{/unless}}
            {{#if showSeeAll}}
                <a class="c-see-all" href="#" data-js="see-all-appointments">See All →</a>
            {{/if}}
        </div>
    </header>
    {{#if appointments.length}}
        <div class="c-appointments-container">
            {{#if groupedAppointments}}
                {{! Month-grouped layout for dedicated Appointments page }}
                {{#each groupedAppointments}}
                    <div class="m-month-group">
                        <div class="c-mg-header">{{this.month}}</div>
                        <div class="c-ac-grid">
                            {{#each this.appointments}}
                                {{> partials/appointment-card-item}}
                            {{/each}}
                        </div>
                    </div>
                {{/each}}
            {{else}}
                {{! Flat grid layout for Overview page }}
                <div class="c-ac-grid">
                    {{#each appointments}}
                        {{> partials/appointment-card-item}}
                    {{/each}}
                </div>
            {{/if}}
        </div>
    {{else}}
        <div class="c-empty">
            <svg class="c-sbciw-image">
                <use
                    xlink:href="#module--customer-portal--appointments"
                ></use></svg>
            <p class="title">No Appointments</p>
            <p class="subtitle">
                It looks like your calendar is wide open —<br />
                no appointments on the horizon!
            </p>
        </div>
    {{/if}}
</div>
