@use "~@cac-sass/config/global" with (
    $legacy: false
);
@use "~@cac-sass/base";
@use "~@cac-sass/app/form";
@use "~@cas-layout-sass/layout";
@use "~@fancyapps/fancybox/dist/jquery.fancybox";

.a-pic-logo,
.a-p-image {
    display: none;
}

:root {
    /* brand colors - mapped to Figma Company Colors design tokens */
    --blue‑50: #{base.$color-primary-light-4};
    --blue‑500: #{base.$color-primary-default};

    /* grey colors - mapped to Figma design tokens */
    --gray‑25: #{base.$color-white-default};
    --gray‑50: #{base.$color-grey-light-4};
    --gray‑100: #{base.$color-grey-light-4};
    --gray‑300: #{base.$color-grey-light-3};
    --gray‑500: #{base.$color-grey-default};
    --gray‑700: #{base.$color-grey-dark-2};
    --gray-light-3: #{base.$color-grey-light-3};

    /* Figma Company Colors - Primary tokens */
    --primary-initial: #{base.$color-primary-default}; /* Maps to Figma Primary/Initial */
    --primary-gradient-alt: #789EF2; /* Maps to Figma Primary/Gradient-Alt (Craftsmen: #789EF2) */
    --primary-hover: #000000; /* Maps to Figma Primary/Primary-Hover */
    --primary-text: #000000; /* Maps to Figma Primary/Text */
    --primary-text-hover: #FFFFFF; /* Maps to Figma Primary/Text-Hover */

    /* Figma Company Colors - Tab tokens */
    --tab-text: #{base.$color-primary-default}; /* Maps to Figma Tab/Text (Craftsmen: #1551D8) */
    --tab-background: #{rgba(base.$color-primary-default, 0.2)}; /* Maps to Figma Tab/Background (20%) */
    --tab-background-hover: #{base.$color-background-form}; /* Maps to Figma Tab/Background-Hover */
    --tab-border: #{rgba(base.$color-primary-default, 0.3)}; /* Maps to Figma Tab/Border (30%) */

    /* Figma Company Colors - Secondary */
    --secondary-background: #FFFFFF; /* Maps to Figma Secondary/Background */
    --secondary-background-hover: #{rgba(base.$color-primary-default, 0.1)}; /* Maps to Figma Secondary/Background-Hover (10%) */
    --secondary-stroke: #{base.$color-grey-light-4}; /* Maps to Figma Secondary/Stroke (gray/light-4) */
    --secondary-stroke-hover: #{base.$color-primary-default}; /* Maps to Figma Secondary/Stroke-Hover */
    --secondary-text: #{base.$color-grey-dark-1}; /* Maps to Figma Secondary/Text (gray/dark-1) */
    --secondary-text-hover: #{base.$color-primary-default}; /* Maps to Figma Secondary/Text-Hover */

    --background-form: #{base.$color-background-form};

    /* Customer Details - Responsive spacing variables */
    --notes-padding-desktop: 8px;
    --notes-padding-tablet: 8px;
    --notes-padding-mobile: 16px;
    --avatar-size-desktop: 40px;
    --avatar-size-tablet: 40px;
    --avatar-size-mobile: 32px;
    --note-card-padding-desktop: 12px;
    --note-card-padding-tablet: 12px;
    --note-card-padding-mobile: 8px;
    --closed-note-padding-desktop: 80px;
    --closed-note-padding-tablet: 40px;
    --closed-note-padding-mobile: 0px;
}

/* ---------- Reset / Base ---------- */
*,
*::before,
*::after {
    box-sizing: border-box;
}
html {
    font-family:
        "Inter",
        system-ui,
        -apple-system,
        BlinkMacSystemFont,
        "Segoe UI",
        Roboto,
        "Helvetica Neue",
        Arial,
        sans-serif;
    line-height: 1.4;
    font-size: 14px;
    color: var(--gray‑700);
    scroll-behavior: smooth;
    height: 100%;
    overflow: hidden;
}
body {
    margin: 0;
    background: var(--gray‑50);
    height: 100%;
    overflow: hidden;
}
a {
    color: var(--blue‑500);
    text-decoration: none;
}
a:hover {
    text-decoration: underline;
}
img {
    display: block;
    max-width: 100%;
    height: auto;
}
button {
    font-family: inherit;
    font-size: inherit;
    color: inherit;
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
}

.m-customer-portal {
    background: var(--secondary-background);

    border-radius: 104px var(--page-radius, 12px) var(--page-radius, 12px)
        var(--page-radius, 12px);
    margin: 24px;
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    height: calc(100vh - 48px);
    max-height: calc(100vh - 48px);
    overflow: hidden;

    @include base.respond-to('<=small') {
        margin: 0;
        border-radius: 0;
        height: 100vh;
        max-height: 100vh;
    }

    .t-hidden {
        display: none !important;
    }

    .section-loading {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background: #{rgba(base.$color-white-default, 0.5)} url('~@cac-public/images/loading_blue.svg') no-repeat center;
        background-size: base.unit-rem-calc(80px) base.unit-rem-calc(80px);
        z-index: 120;
        display: none;
    }

    .c-header {
        display: flex;
        align-items: flex-start;
        gap: 0;
        padding: 24px 0 0 24px;
        background: var(--secondary-background);
        overflow-y: visible;

        @include base.respond-to("<=xlarge") {
            margin-bottom: base.unit-rem-calc(-2.5px);
        }

        @include base.respond-to('<=small') {
            padding-left: 0;
            margin-bottom: 0;
            border-bottom: 0;
        }

        .c-h-logo {
            width: base.unit-rem-calc(320px);
            height: base.unit-rem-calc(160px);
            padding: base.unit-rem-calc(24px);
            flex-direction: column;
            justify-content: center;
            align-items: center;
            border-radius: 1000px;
            border: 1px solid var(--secondary-stroke);
            background: var(--secondary-background);
            margin-bottom: base.unit-rem-calc(-78px);
            z-index: 10;
            flex-shrink: 0;
            display: flex;
            position: relative;

            @include base.respond-to("<=xlarge") {
                width: base.unit-rem-calc(240px);
                height: base.unit-rem-calc(120px);
                padding: base.unit-rem-calc(24px);
                border-radius: 120px 60px 0 120px;
                margin-bottom: 0;
            }

            @include base.respond-to('<=small') {
                width: base.unit-rem-calc(192px);
                height: base.unit-rem-calc(96px);
                padding: base.unit-rem-calc(16px);
                border-radius: 0 0 48px 0;
                border: 1px solid var(--secondary-stroke);
                border-top: none;
                border-left: none;
                margin-bottom: 0;
                position: absolute;
                top: 0;
                left: 0;
            }

            .c-hl-info-button {
                display: none;
                position: absolute;
                bottom: 8px;
                right: 8px;
                width: 18px;
                height: 18px;
                border-radius: 50%;
                align-items: center;
                justify-content: center;
                color: var(--gray-light-3);
                z-index: 3;
                transition: color 0.15s ease-in-out;
                cursor: pointer;

                @include base.respond-to("<=xlarge") {
                    display: flex;
                    animation: c-hl-info-button-appear 0.4s ease-out 0.15s forwards;
                }

                @include base.respond-to('<=small') {
                    top: 8px;
                    bottom: auto;
                }

                &::before {
                    content: '';
                    position: absolute;
                    top: -2px;
                    left: -2px;
                    right: -2px;
                    bottom: -2px;
                    border: 1px solid var(--blue‑500);
                    border-radius: 50%;
                    opacity: 0;
                    transform: scale(0.1);
                    pointer-events: none;

                    @include base.respond-to("<=xlarge") {
                        animation: c-hl-info-button-border-pulse 0.8s ease-out 0.15s forwards;
                    }
                }

                svg {
                    width: 18px;
                    height: 18px;
                }

                @include base.respond-to("hover") {
                    &:hover {
                        color: var(--gray‑700);
                    }
                }
            }
        }

        @keyframes c-hl-info-button-appear {
            0% {
                opacity: 0;
                transform: scale(0.4);
            }
            10% {
                opacity: 1;
                transform: scale(1);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }

        @keyframes c-hl-info-button-border-pulse {
            0% {
                opacity: 0;
                transform: scale(1);
            }
            45% {
                opacity: 0.8;
                transform: scale(1.2);
            }
            90% {
                opacity: 0.4;
                transform: scale(1);
            }
            100% {
                opacity: 0;
                transform: scale(1.2);
            }
        }

        .c-h-navigation {
            display: flex;
            flex-direction: column;
            border-bottom: 1px solid var(--secondary-stroke);
            position: relative;
            justify-content: flex-end;
            overflow-x: hidden;
            overflow-y: visible;
           @include base.full-width-height;

            @include base.respond-to("<=xlarge") {
                margin-top: base.unit-rem-calc(-2.5px);
            }

            @include base.respond-to('<=small') {
                margin-top: base.unit-rem-calc(96px);
                padding-left: 0;
            }

            .c-hn-extra-navigation {
                display: none;
                height: base.unit-rem-calc(60px);
                padding: 16px 12px 16px 0;
                justify-content: flex-end;
                align-items: center;
                align-self: stretch;
                overflow-x: hidden;
                overflow-y: hidden;

                @include base.respond-to("<=xlarge") {
                    display: flex;
                    border-bottom: 1px solid var(--secondary-stroke);
                }

                @include base.respond-to("<=small") {
                    border: none;
                }

                .c-hn-user-profile{
                    @include base.respond-to('<=small') {
                        position: absolute;
                        top: base.unit-rem-calc(10px);
                        right: 0;
                        z-index: 15;
                    }
                    .c-hnup-pill {
                        @include base.respond-to('<=small') {
                            background:none;
                            border:none;
                            margin:0;
                            padding:0;
                        }
                    }
                }
            }

            .c-hn-main-navigation {
                display: flex;
                width: 100%;
                gap: base.unit-rem-calc(8px);
                justify-content: flex-start;
                align-items: center;
                padding: base.unit-rem-calc(12px) base.unit-rem-calc(12px);
                overflow-x: auto;
                overflow-y: hidden;

                @include base.respond-to("<=small") {
                    padding-top: base.unit-rem-calc(20px);
                    border:0;
                }

                .c-hn-user-profile {
                    @include base.respond-to("<=xlarge") {
                        display: none;
                    }
                }

            }
            @include base.respond-to("<small") {
                margin: 0;
                &::before {
                    right: 0;
                }
                &::after {
                    left: 0;
                }
            }
            .c-hnm-item {
                cursor: pointer;
                height: base.unit-rem-calc(40px);
                display: flex;
                align-items: center;
                justify-content: center;
                gap: base.unit-rem-calc(8px);
                border-radius: base.unit-rem-calc(40px);
                padding: 0 base.unit-rem-calc(12px);
                color: var(--secondary-text);
                transition:
                    background-color 0.35s linear,
                    padding 0.25s ease-in-out;
                @include base.respond-to("hover") {
                    &:hover {
                        padding: 0 base.unit-rem-calc(24px);
                        background-color: var(--gray‑50);

                    }
                }
                &.t-active {
                    color: var(--tab-text);
                    border-radius: var(--border-radius, 100px);
                    border: 1px solid var(--tab-border);
                    background: var(--tab-background);
                    padding: 0 base.unit-rem-calc(24px);
                    .c-hnmi-icon,
                    .c-hnmi-title {
                        color: var(--tab-text);
                    }
                }
                &.t-disabled {
                    cursor: not-allowed;
                    color: base.$color-grey-light-4;
                    .c-hnmi-icon,
                    .c-hnmi-title {
                        cursor: not-allowed;
                        color: base.$color-grey-light-4;
                    }
                    @include base.respond-to("hover") {
                        &:hover {
                            padding: 0 base.unit-rem-calc(12px);
                            background: transparent;
                            color: base.$color-grey-light-4;
                            .c-hnmi-icon,
                            .c-hnmi-title {
                                color: base.$color-grey-light-4;
                            }
                        }
                    }
                }

                a {
                    text-decoration: none;
                }
            }
            .c-hnmi-icon {
                flex: 0 0 auto;
                @include base.svg-icon("default-18");
                color: base.$color-grey-dark-1;
            }
            .c-hnmi-title {
                flex: 0 0 auto;
                @include base.typo-header(
                    $size: 14px,
                    $line-height: base.unit-rem-calc(20px)
                );
                color: base.$color-grey-dark-1;
                white-space: nowrap;
            }
            .c-hn-user-profile {
                margin-left: auto;
                margin-right: base.unit-rem-calc(20px);

                .c-hnup-pill {
                    display: flex;
                    align-items: center;
                    height: base.unit-rem-calc(40px);
                    padding: 8px 0 8px 0;
                    border-radius: 32px 0 0 32px;
                    background: var(--tab-background);
                    font: 600 14px/1 var(--font-sans, "Inter", sans-serif);
                    color: var(--pill-fg);
                    cursor: pointer;
                    position: relative;
                    transition: box-shadow 0.15s;
                }
                .c-hnup-text {
                    margin: 0 base.unit-rem-calc(24px) 0 base.unit-rem-calc(24px);
                    color: var(--tab-text);
                    @include base.respond-to('<=small') {
                        display: none;
                    }
                }

                .c-hnup-icon {
                    width: base.unit-rem-calc(40px);
                    height: base.unit-rem-calc(40px);
                    margin-right: base.unit-rem-calc(-20px);
                    display: grid;
                    place-content: center;
                    border-radius: var(--border-radius, 100px);
                    border: 1px solid var(--secondary-stroke);
                    background: var(--secondary-background);
                    box-shadow: 0 4px 8px -4px #{rgba(base.$color-grey-light-1, 0.20)}, 0 1px 8px -8px #{rgba(base.$color-grey-light-1, 0.20)};
                    z-index: 10;
                    @include base.respond-to('<=small') {
                        margin-right: 0;
                        position: relative;
                    }
                    svg {
                        width: base.unit-rem-calc(18px);
                        height: base.unit-rem-calc(18px);
                        color: var(--secondary-text);
                    }
                }
            }
        }
    }

    .c-container-scrollable {
        display: flex;
        flex: 1;
        min-height: 0;
        overflow: hidden;
    }
    .c-cs-tab-sections {
        display: flex;
        flex: 1;
        min-height: 0;
        overflow: hidden;
    }

    .m-pages {
        flex: 1;
        display: flex;
        overflow: hidden;
        min-height: 0;
        position: relative;
    }
    .c-page {
        display: flex;
        flex: 1;
        min-height: 0;

        .c-p-column {
            flex: 1;
            padding: 0;
            display: flex;
            flex-direction: column;
            gap: 0;
            min-height: 0;
            overflow: hidden;
        }

        .c-p-column.appointments-bids-column {
            min-width: base.unit-rem-calc(290px);
        }

        .c-p-column.invoices-column {
            min-width: base.unit-rem-calc(311px);
        }
    }

    .c-card {
        background: var(--secondary-background);
        border: 1px solid var(--gray‑100);
        display: flex;
        flex-direction: column;
        flex: 1;
        border-top: none;
        border-right: none;
        min-height: 0;
        overflow: hidden;
    }

    .c-card:first-child {
    }

    .c-card > *:last-child {
        flex: 1;
        min-height: 0;
        overflow-y: auto;
        overflow-x: hidden;
    }

    .c-card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        border-bottom: 1px solid var(--gray‑100);
    }
    .c-card-header h3 {
        font-size: 14px;
        font-weight: 600;
        color: var(--gray‑700);
    }
    .c-ch-actions {
        display: flex;
        align-items: center;
        gap: 8px;
        flex-shrink: 0;

        @include base.respond-to("<=small") {
            gap: 4px;
        }
    }

    .c-see-all {
        font-size: 12px;
        font-weight: 500;
        color: var(--gray‑700);
        padding: 4px 8px;
        border: 1px solid var(--gray‑100);
        border-radius: 4px;
        display: flex;
        align-items: center;
        gap: 4px;
    }
    .c-see-all:hover {
        background: var(--gray‑50);
    }

    .c-date-sort-toggle {
        font-size: 12px;
        font-weight: 500;
        color: var(--gray‑700);
        padding: 4px 8px;
        border: 1px solid var(--gray‑100);
        border-radius: 4px;
        display: flex;
        align-items: center;
        gap: 4px;
        background: var(--secondary-background);
        cursor: pointer;
        transition: background-color 0.15s ease-in-out;
        white-space: nowrap;
        flex-shrink: 0;

        @include base.respond-to("<=small") {
            font-size: 11px;
            padding: 3px 6px;
            gap: 3px;
        }

        @include base.respond-to("hover") {
            &:hover {
                background: var(--gray‑50);
            }
        }
    }
    .c-dst-text {
        flex: 0 0 auto;
    }
    .c-dst-icon {
        width: 12px;
        height: 12px;
        flex-shrink: 0;
        transition: transform 0.15s ease-in-out;

        @include base.respond-to("<=small") {
            width: 10px;
            height: 10px;
        }
    }
    .c-date-sort-toggle[data-sort-order="oldest"] .c-dst-icon {
        transform: rotate(180deg);
    }

    .c-empty {
        padding: 32px 16px;
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 12px;
        min-height: 180px;
        justify-content: center;

        .c-empty img {
            opacity: 0.55;
        }
        .c-empty .title {
            font-weight: 600;
            color: var(--gray‑700);
        }
        .c-empty .subtitle {
            font-size: 12px;
            color: var(--gray‑500);
            line-height: 1.3;
        }
    }

    .m-content-box {
        background: var(--secondary-background);
        border: 1px solid var(--gray‑100);
        border-radius: 8px;
        box-shadow:
            0 1px 3px 0 #{rgba(#000, 0.1)},
            0 1px 2px 0 #{rgba(#000, 0.06)};
        padding: 16px;
        margin-bottom: 12px;

        transition:
            box-shadow 0.15s ease-in-out,
            border-color 0.15s ease-in-out;

        @include base.respond-to("hover") {
            &:hover {
                box-shadow:
                    0 4px 6px -1px #{rgba(#000, 0.1)},
                    0 2px 4px -1px #{rgba(#000, 0.06)};
                border-color: var(--gray‑300);
            }
        }

        &:last-child {
            margin-bottom: 0;
        }

        .m-content-box.invoices-card {
            min-width: base.unit-rem-calc(388px);
        }
    }

    /* ---------- Tab-specific styles ---------- */
    .m-appointments,
    .m-bids,
    .m-invoices {
        .m-content-box {
            max-width: base.unit-rem-calc(400px);
        }

    }

    .m-gallery {
        .m-date-group .c-dg-grid {
            grid-template-columns: repeat(auto-fit, minmax(base.unit-rem-calc(285px), 1fr));
        }
    }

    /* ---------- Grid layouts for customer portal cards ---------- */
    .c-ac-grid,
    .c-bc-grid,
    .c-ic-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 24px;

        @include base.respond-to('<=medium') {
            flex-direction: column;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 12px;
            padding: 12px;
        }

        .m-content-box {
            display: flex;
            flex-direction: column;
            flex: 0 0 auto;
            width: base.unit-rem-calc(320px);
            min-width: base.unit-rem-calc(320px);
            margin-bottom: 0;

            @include base.respond-to('<=medium') {
                min-width: auto;
            }
        }
    }

    .c-bc-grid,
    .c-ic-grid {
        .m-content-box {
            min-width: base.unit-rem-calc(388px);
            width: base.unit-rem-calc(388px);

            @include base.respond-to('<=medium') {
                min-width: auto;
            }
        }
    }

    .c-cards-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 12px;
        padding: 12px;

        @include base.respond-to('<=medium') {
            grid-template-columns: 1fr;
            gap: 8px;
            padding: 8px;
        }
    }

    /* Overview page */
    .m-overview {
        .c-desktop-layout {
            display: grid;
            gap: 0;
            height: 100%;

            grid-template-columns: 1fr 1fr 1fr;
            grid-template-rows: 1fr;
            grid-template-areas: "appointments-bids invoices uploads";

            @include base.respond-to('<=medium') {
                grid-template-columns: 1fr 1fr;
                grid-template-rows: 1fr 1fr;
                grid-template-areas:
                    "appointments-bids invoices"
                    "appointments-bids uploads";
                background-color: #{base.$color-red-light-4};
            }

            @include base.respond-to('<=small') {
                display: none;
            }
        }

        .c-mobile-carousel {
            display: none;

            @include base.respond-to('<=small') {
                display: block;
                position: relative;
                //width: 100%;
                //height: 100%;
                overflow: hidden;
                //background: #fff;
            }
        }

        .c-desktop-layout .c-p-column {
            display: flex;
            flex-direction: column;
            overflow: hidden;

            @include base.respond-to('<=medium') {
                min-width: 0;
            }

            &:nth-child(1) {
                grid-area: appointments-bids;

                @include base.respond-to('<=medium') {
                    grid-area: appointments-bids !important;
                }

                .c-card {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    min-height: 0;
                    border-right: 1px solid var(--gray‑100);

                    &:first-child {
                        border-bottom: 1px solid var(--gray‑100);
                    }

                    .c-appointments-container,
                    .c-bids-container {
                        flex: 1;
                        overflow-y: auto;
                    }
                }
            }

            &:nth-child(2) {
                grid-area: invoices;

                @include base.respond-to('<=medium') {
                    grid-area: invoices !important;
                }

                .c-card {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    min-height: 0;
                    border-right: 1px solid var(--gray‑100);

                    @include base.respond-to('<=medium') {
                        border-bottom: 1px solid var(--gray‑100);
                    }

                    .c-invoices-container {
                        flex: 1;
                        overflow-y: auto;
                    }
                }
            }

            &:nth-child(3) {
                grid-area: uploads;

                @include base.respond-to('<=medium') {
                    grid-area: uploads !important;
                }

                .c-card {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    min-height: 0;

                    .c-uploads-container {
                        flex: 1;
                        overflow-y: auto;
                    }
                }
            }
        }

        .c-ac-grid,
        .c-bc-grid,
        .c-ic-grid {
            flex-direction: column;

            .m-content-box {
                width: 100%;
                min-width: auto;

                @include base.respond-to('<=medium') {
                    min-width: auto;
                }
            }
        }

        .c-mc-container {
            @include base.respond-to('<=small') {
                display: flex;
                width: 400%;
                height: 100%;
                transition: transform 0.3s ease-in-out;
            }
        }

        .c-mc-slide {
            @include base.respond-to('<=small') {
                width: 25%;
                height: 100%;
                flex-shrink: 0;
                display: flex;
                flex-direction: column;
                padding: 0;
                box-sizing: border-box;
            }

            .c-card {
                @include base.respond-to('<=small') {
                    max-width: 100vw;
                    border: none;
                    box-shadow: none;
                    border-radius: 0;
                    background: transparent;
                }
            }
        }

        .c-mc-navigation {
            @include base.respond-to('<=small') {
                position: fixed;
                bottom: base.unit-rem-calc(20px);
                left: 50%;
                transform: translateX(-50%);
                display: flex;
                justify-content: center;
                align-items: center;
                gap: base.unit-rem-calc(10px);
                z-index: 100;
                padding: 12px 14px 12px 12px;
                background: var(--background-form);
                border-radius: base.unit-rem-calc(100px);
                backdrop-filter: blur(12px);
                background: var(--background-form);
            }
        }

        .c-mcn-dot {
            @include base.respond-to('<=small') {
                width: base.unit-rem-calc(8px);
                height: base.unit-rem-calc(8px);
                border-radius: 100px;
                background: var(--secondary-stroke);
                cursor: pointer;
                transition: all 0.3s ease;

                &.t-active {
                    width: base.unit-rem-calc(28px);
                    height: base.unit-rem-calc(12px);
                    background: var(--primary-initial);
                }
            }
        }
    }

    .m-appointment-card {
        .c-ac-type {
            font-size: 14px;
            font-weight: 600;
            color: var(--gray‑700);
            margin-bottom: 16px;
            line-height: 1.2;
        }

        .c-ac-details {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-bottom: 10px;
        }

        .c-acd-row {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 13px;
            line-height: 1.4;

            svg {
                width: 16px;
                height: 16px;
                flex-shrink: 0;
                color: var(--gray‑500);
            }
        }

        .c-acdr-text {
            min-width: 60px;
            font-weight: 500;
            color: var(--gray‑700);
        }

        .c-acdr-value {
            font-weight: 400;
            color: var(--gray‑700);
        }

        .c-ac-actions {
            padding: 0 0 0 4px;

            .c-aca-btn {
                display: inline-flex;
                align-items: center;
                gap: 8px;
                border: 1px solid var(--gray‑100);
                background: var(--secondary-background);
                color: var(--gray‑700);
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 13px;
                box-shadow:
                    0 4px 8px -4px rgba(119, 141, 166, 0.2),
                    0 1px 8px -8px rgba(119, 141, 166, 0.2);
            }
            @include base.respond-to("hover") {
                .c-icfa-btn:hover {
                    background: var(--gray‑50);
                }
            }
        }
    }

    /* ---------- Bid Card Module ---------- */
    .m-bid-card {
        .c-bc-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
        }

        .c-bch-title-section {
            flex: 1;
            min-width: 0;
        }

        .c-bchts-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--gray‑700);
            line-height: 1.3;
            margin-bottom: 4px;
        }

        .c-bchts-number {
            font-size: 13px;
            font-weight: 400;
            color: var(--gray‑500);
            line-height: 1.2;
            font-style: italic;
        }

        .c-bch-download {
            flex-shrink: 0;
            margin-left: 12px;
        }

        .c-bchd-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            color: var(--secondary-text);
            text-align: center;
            font-feature-settings:
                "liga" off,
                "clig" off;
            font-family: Roboto, sans-serif;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px; /* 142.857% */
            background: none;
            border: none;
            padding: 6px 8px;
            cursor: pointer;
            text-decoration: none;
            transition: color 0.15s ease-in-out;
            border-radius: 4px;

            svg {
                width: 14px;
                height: 14px;
                flex-shrink: 0;
            }

            @include base.respond-to("hover") {
                &:hover {
                    color: var(--gray‑700);
                    background: var(--gray‑50);
                }
            }
        }

        .c-bc-address {
            margin-bottom: 20px;
        }

        .c-bca-line {
            font-size: 14px;
            font-weight: 400;
            color: var(--gray‑700);
            line-height: 1.4;
            margin-bottom: 2px;

            &:last-child {
                margin-bottom: 0;
            }
        }

        .c-bc-total {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 0;
            margin-bottom: 16px;
        }

        .c-bct-label {
            color: var(--gray‑700);
            font-feature-settings:
                "liga" off,
                "clig" off;
            font-family: Roboto, sans-serif;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px;
            flex: 0 0 auto;
        }

        .c-bct-divider {
            height: 1px;
            background: var(--gray‑100);
            flex: 1 1 auto;
            border-radius: 1px;
        }

        .c-bct-amount {
            color: var(--gray‑700);
            font-feature-settings:
                "liga" off,
                "clig" off;
            font-family: Roboto, sans-serif;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px;
            flex: 0 0 auto;
        }

        .c-bcf-action {
            display: flex;
            justify-content: flex-end;

            button {
                display: flex;
                height: 32px;
                padding: 0 16px;
                justify-content: center;
                align-items: center;
                border-radius: 100px;
                border: 1px solid transparent;
                background: var(--primary-initial);
                box-shadow:
                    0 4px 8px -4px #{rgba(base.$color-grey-light-1, 0.3)},
                    0 1px 8px -8px #{rgba(base.$color-grey-light-1, 0.3)};
                text-transform: none;
            }
        }

        .c-bc-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .c-bcfa-btn {
            background: var(--blue‑500);
            color: var(--secondary-background);
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            transition: background-color 0.15s ease-in-out;

            @include base.respond-to("hover") {
                &:hover {
                    background: var(--primary-active);
                }
            }
        }

        .c-bcf-date {
            flex: 1;
            text-align: right;
            margin-left: 12px;
        }

        .c-bcfd-text {
            color: var(--secondary-text);
            font-feature-settings:
                "liga" off,
                "clig" off;
            font-family: Roboto, sans-serif;
            font-size: 13px;
            font-style: italic;
            font-weight: 400;
            line-height: 20px; /* 153.846% */
        }
    }

    .c-appointments-container,
    .c-bids-container,
    .c-invoices-container {
        padding: 16px;
    }

    .c-bids-container {
        padding: 16px;
    }

    .m-invoice-card {
        .c-ic-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--gray‑700);
            line-height: 1.3;
            margin-bottom: 8px;
        }

        .c-ic-subtitle {
            font-size: 13px;
            color: var(--gray‑700);
            margin-bottom: 16px;
            .c-ics-note {
                color: var(--gray‑500);
                font-style: italic;
            }
        }

        .c-ic-rows {
            display: flex;
            flex-direction: column;
            gap: 1px;
        }
        .c-ic-row {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        .c-icr-label {
            color: var(--gray‑700);
            font-feature-settings:
                "liga" off,
                "clig" off;
            font-family: Roboto, sans-serif;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px;
            flex: 0 0 auto;
        }
        .c-icr-divider {
            height: 1px;
            background: var(--gray‑100);
            flex: 1 1 auto;
            border-radius: 1px;
        }
        .c-icr-amount {
            color: var(--gray‑700);
            font-feature-settings:
                "liga" off,
                "clig" off;
            font-family: Roboto, sans-serif;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px;
            flex: 0 0 auto;
            min-width: base.unit-rem-calc(62px);
        }

        .c-ic-footer {
            display: flex;
            flex-direction: column;
            margin-top: 16px;

            .c-icf-action {
                order: 1;
                align-self: flex-start;
                margin-bottom: 8px;
            }
            .c-icf-date {
                order: 2;
                align-self: flex-end;
            }
        }

        .c-icfa-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            border: 1px solid var(--gray‑100);
            background: var(--secondary-background);
            color: var(--gray‑700);
            border-radius: 8px;
            padding: 8px 12px;
            font-size: 13px;
            box-shadow:
                0 4px 8px -4px #{rgba(base.$color-grey-light-1, 0.2)},
                0 1px 8px -8px #{rgba(base.$color-grey-light-1, 0.2)};
        }
        @include base.respond-to("hover") {
            .c-icfa-btn:hover {
                background: var(--gray‑50);
            }
        }
        .c-icfab-icon {
            width: 14px;
            height: 14px;
        }
        .c-icf-date {
            font-size: 12px;
            color: var(--gray‑500);
            font-style: italic;
        }
    }

    /* ---------- Invoices Container Component ---------- */
    .c-invoices-container {
        padding: 16px;
    }

    .c-uploads-container {
        padding: 16px;
        display: flex;
        flex-direction: column;
        gap: 24px;
    }

    .m-date-group {
        .c-dg-header {
            color: var(--secondary-text);
            font-feature-settings:
                "liga" off,
                "clig" off;
            font-family: Roboto, sans-serif;
            font-size: 14px;
            font-style: normal;
            font-weight: 500;
            line-height: 20px;
            margin-bottom: 16px;
        }

        .c-dg-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(base.unit-rem-calc(180px), 1fr));
            gap: 12px;

            @include base.respond-to('<=medium') {
                grid-template-columns: 1fr;
                gap: 8px;
            }
        }
    }

    .m-month-group {
        margin-bottom: 32px;

        @include base.respond-to('<=medium') {
            margin-bottom: 24px;
        }

        &:last-child {
            margin-bottom: 0;
        }

        .c-mg-header {
            color: var(--secondary-text);
            font-feature-settings:
                    "liga" off,
                    "clig" off;
            font-family: Roboto, sans-serif;
            font-size: 14px;
            font-style: normal;
            font-weight: 500;
            line-height: 20px;
            margin-bottom: 16px;

            @include base.respond-to('<=medium') {
                font-size: 14px;
                margin-bottom: 16px;
            }
        }
    }

    .m-image-card {
        position: relative;
        background: var(--secondary-background);
        border-radius: 8px;
        overflow: hidden;
        box-shadow:
            0 1px 3px 0 #{rgba(#000, 0.1)},
            0 1px 2px 0 #{rgba(#000, 0.06)};
        transition:
            box-shadow 0.15s ease-in-out,
            transform 0.15s ease-in-out;
        cursor: pointer;
        aspect-ratio: 4/3;

        &:hover {
            box-shadow:
                0 4px 6px -1px #{rgba(#000, 0.1)},
                0 2px 4px -1px #{rgba(#000, 0.06)};
            transform: translateY(-1px);

            .c-imc-image img {
                transform: scale(1.05);
            }
        }

        .c-imc-image {
            background: var(--gray‑50);
            position: relative;
            overflow: hidden;

            img {
                object-fit: cover;
                object-position: center;
                display: block;
                transition: transform 0.15s ease-in-out;
            }

            .c-imci-placeholder {
                display: flex;
                align-items: center;
                justify-content: center;
                background: var(--gray‑50);
                color: var(--gray‑300);

                svg {
                    width: 32px;
                    height: 32px;
                }
            }
        }

        .c-imc-number {
            position: absolute;
            top: 8px;
            right: 8px;
            background: var(--primary-initial);
            color: var(--primary-text);
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
            line-height: 1;
            z-index: 3;
        }

        .c-imc-info-button {
            position: absolute;
            bottom: 8px;
            left: 8px;
            width: 34px;
            height: 34px;
            background: #{rgba(#000, 0.6)};
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--secondary-background);
            z-index: 3;
            transition: background-color 0.15s ease-in-out;

            svg {
                width: 18px;
                height: 18px;
                fill: currentColor;
            }

            &:hover {
                background: #{rgba(#000, 0.8)};
            }
        }

        .c-imc-info {
            @include base.respond-to('<=medium') {
                padding: 10px;

                .c-imci-name {
                    font-size: 12px;
                }

                .c-imci-meta {
                    font-size: 11px;
                }
            }
        }
    }
}

/* Company Information Sidebar and Modal */
.s-modal .m-company-info {
    display: flex;
    width: 100%;
    margin: 0;
    padding: 0;
    background: transparent;
    border: none;
    box-shadow: none;
}

/* Customer Information Modal */
.s-modal .m-customer-info-modal {
    display: flex;
    width: 100%;
    margin: 0;
    padding: 0;
    background: transparent;
    border: none;
    box-shadow: none;
}

.m-customer-info-modal {
    display: flex;
    flex-direction: column;
    gap: base.unit-rem-calc(20px);

    .c-cim-section {
        display: flex;
        flex-direction: column;
        gap: base.unit-rem-calc(6px);
    }

    .c-cim-label {
        color: var(--gray-dark-4, #1F252C);
        font-feature-settings: 'liga' off, 'clig' off;
        font-family: Roboto, sans-serif;
        font-size: 13px;
        font-style: normal;
        font-weight: 500;
        line-height: 20px;
        margin: 0;
    }

    .c-cim-value {
        color: var(--gray-dark-4, #1F252C);
        font-feature-settings: 'liga' off, 'clig' off;
        font-family: Roboto, sans-serif;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px; /* 142.857% */
    }

    .c-cim-actions {
        display: flex;
        justify-content: flex-end;
        margin-top: base.unit-rem-calc(8px);
    }

    .c-cim-request-btn {
        display: inline-flex;
        align-items: center;
        gap: base.unit-rem-calc(8px);
        padding: base.unit-rem-calc(8px) base.unit-rem-calc(12px);
        color: var(--gray-dark-2, #435162);
        text-align: center;
        font-feature-settings: 'liga' off, 'clig' off;
        font-family: Roboto, sans-serif;
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: 20px; /* 142.857% */
        cursor: pointer;
        text-decoration: none;
        transition:
            background-color 0.15s ease-in-out,
            border-color 0.15s ease-in-out,
            color 0.15s ease-in-out;
        @include base.respond-to("hover") {
            &:hover {
                background: var(--tab-background);
                border-color: var(--tab-border);
                color: var(--tab-text);
            }
        }

        .c-cimr-text {
            flex: 0 0 auto;
        }

        .c-cimr-icon {
            flex: 0 0 auto;
            width: base.unit-rem-calc(16px);
            height: base.unit-rem-calc(16px);
        }
    }
}

.m-company-info {
    display: flex;
    padding: 90px 12px 40px 12px;
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    align-self: stretch;
    min-height: 0;
    overflow-y: auto;

    @include base.respond-to('<=xlarge') {
        display: none;
    }
    h4 {
        color: #{base.$color-grey-dark-4};
        font-variant-numeric: slashed-zero;
        font-feature-settings:
                "liga" off,
                "clig" off;
        font-family: Barlow, sans-serif;
        font-size: 20px;
        font-style: normal;
        font-weight: 600;
        line-height: 140%;
    }
    .c-ci-name {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 16px;
    }
    .c-ci-block {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        gap: 16px;
        width: 100%;
        min-width: base.unit-rem-calc(340px);
        .c-ci-text {
            flex: 1 1 auto;
            min-width: 0;
            h6 {
                color: #{base.$color-grey-dark-4};
                font-feature-settings:
                        "liga" off,
                        "clig" off;
                font-family: Barlow, sans-serif;
                font-size: 14px;
                font-style: normal;
                font-weight: 600;
                line-height: 140%;
            }
        }
        .c-action-icon {
            display: flex;
            padding: 10px;
            justify-content: center;
            align-items: center;
            gap: 8px;
            border-radius: var(--border-radius, 100px);
            border: 1px solid var(--secondary-stroke);
            background: var(--secondary-background);
            box-shadow:
                    0 4px 8px -4px #{rgba(base.$color-grey-light-1, 0.2)},
                    0 1px 8px -8px #{rgba(base.$color-grey-light-1, 0.2)};
            svg,
            img {
                width: 16px;
                height: 16px;
            }
        }
    }
    .c-ci-title {
        font-size: 12px;
        font-weight: 600;
        color: var(--gray‑500);
        margin-bottom: 6px;
    }
    .c-ci-field {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 12px;
        margin-bottom: 12px;
        position: relative;
        .c-cif-meta {
            flex: 1 1 0;
            min-width: 0;
            display: flex;
            flex-direction: column;
        }
        .c-cif-label {
            color: #{base.$color-grey-dark-4};
            font-feature-settings:
                    "liga" off,
                    "clig" off;
            font-family: Roboto, sans-serif;
            font-size: 13px;
            font-style: normal;
            font-weight: 500;
            line-height: 20px;
        }
        .c-cif-value {
            color: var(--gray‑700);
            font-feature-settings:
                    "liga" off,
                    "clig" off;
            font-family: Roboto, sans-serif;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px;
        }
    }
    .c-salesman {
        flex: 1;
        gap: 12px;
        align-items: flex-start;
        margin: 12px 0;
        .c-s-wrapper {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        .c-sw-avatar {
            display: flex;
            justify-content: center;
            align-items: center;
            width: var(--avatar-size, 40px);
            height: var(--avatar-size, 40px);
            border-radius: 40px;
            background: lightgray 50% / cover no-repeat;
            box-shadow:
                    0 1px 3px 0 #{rgba(base.$color-grey-light-1, 0.08)},
                    0 1px 2px 0 #{rgba(base.$color-grey-light-1, 0.04)};
        }
        .c-sw-name {
            font-weight: 600;
            color: var(--gray-700);
        }
    }
}