/**
 * @module Property/Modals
 */

'use strict';

import Api from '@ca-package/api';
import {Confirm} from '@ca-submodule/modal';

import modal_delete_tpl from '@cam-property-tpl/modals/delete.hbs';

/**
 * @memberof module:Property/Modals
 */
export class Delete extends Confirm {
    /**
     * Constructor
     *
     * @param {module:Property.Controller} controller
     */
    constructor(controller) {
        super();
        this.state.controller = controller;
        this.setTitle('Delete Property');
    };

    /**
     * Open modal
     *
     * @param {Object} data
     */
    open(data) {
        this.state.data = data;
        this.setContent(modal_delete_tpl({
            address: data.address,
        }));
        super.open();
    };

    /**
     * Handle yes which means the user wants to delete
     */
    handleYes() {
        this.startWorking();
        Api.Resources.Properties().delete(this.state.data.id)
            .then(() => {
                this.state.controller.table.deleteRow(this.state.data.id);
                this.resetWorking();
                this.close();
            }, (error) => {
                if (error.code === 1014) {
                    error.message = 'Unable to delete property';
                }
                this.resetWorking();
                this.showErrorMessage(error.message);
            });
    };

    /**
     * Handle no which means the user wants to abort
     */
    handleNo() {
        this.close();
    };

    /**
     * Close modal
     */
    close() {
        this.state.data = null;
        super.close();
    };
}
