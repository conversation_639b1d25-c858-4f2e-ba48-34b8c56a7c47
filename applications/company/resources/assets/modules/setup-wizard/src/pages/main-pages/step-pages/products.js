'use strict';

const accounting = require('accounting');

import Api from '@ca-package/api';
import Page from '@ca-package/router/src/page';
import {findChild, jsSelector, onClickDestroy, onClickWatcher, onEvent} from "@ca-package/dom";

import {Base as Table} from '@ca-submodule/table';
const FormInput = require('@ca-submodule/form-input');
const NumberInput = require('@ca-submodule/form-input/src/number');
FormInput.use(NumberInput);

const {createSuccessMessage} = require('@cas-notification-toast-js/message/success');
import FormValidator from "@cas-validator-js";

import {ProductAdd} from "@cam-setup-wizard-js/pages/main-pages/step-pages/product-pages/add";

import edit_tpl from '@cam-setup-wizard-tpl/pages/main-pages/step-pages/products/edit.hbs';
import products_tpl from '@cam-setup-wizard-tpl/pages/main-pages/step-pages/products.hbs';

/**
 * @memberof module:SetupWizard/Pages/MainPages/StepPages
 */
export class Products extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);
        Object.assign(this.state, {
            parent,
            previous: {
                route: 'step.terms_conditions',
                step: parent.steps.TERMS_CONDITIONS
            },
            next: {
                route: 'step.media',
                step: parent.steps.MEDIA
            },
            table: null,
            setup_products: false,
            setup_products_completed_at: null,
            table_loaded: false,
            table_scope: {
                sorts: {
                    name: Table.Sort.ASC
                }
            },
            child_row_open: false
        });
    };

    /**
     * Get available routes
     *
     * @returns {object}
     */
    static get routes() {
        return {
            add: {
                path: '/add',
                modal: ProductAdd
            }
        };
    };

    /**
     * Set message and show error container
     *
     * @param {string} message
     */
    setError(message) {
        this.state.parent.getPageContainer().scrollTop(0);
        this.elem.error.text(message).addClass('t-show');
    }

    /**
     * Clear and hide error container
     */
    clearError() {
        this.elem.error.text('').removeClass('t-show');
    };

    /**
     * Edit product in table row
     *
     * @param {jQuery} content
     * @param {object} row
     */
    editProduct(content, row) {
        this.state.parent.startWorking();
        let data = {
            'name': findChild(content, jsSelector('name')).val(),
            'unit_id': findChild(content, jsSelector('unit')).val(),
            'is_intangible': false,
            'pricing_type': Api.Constants.ProductItems.PricingTypes.BASIC,
            'prices': [
                {
                    'min_count': '0',
                    'max_count': null,
                    'status': Api.Constants.ProductItemPrices.Status.ACTIVE,
                    'price': findChild(content, jsSelector('price')).val().replace(/,/g, '')
                }
            ]
        };
        Api.Resources.ProductItems().partialUpdate(row.data().id, data).then(() => {
            this.state.parent.resetWorking();
            let message = createSuccessMessage('Product edited successfully');
            this.router.main_route.layout.toasts.addMessage(message);
            this.state.table.toggleChildRow(row, {}, true);
            this.state.child_row_open = false;
            this.state.table.draw();
        }, (error, response) => {
            switch (response.statusCode()) {
                case 422:
                    let item_errors = response.data().errors;
                    console.log(item_errors);
                    this.state.parent.resetWorking();
                    break;
                default:
                    this.state.parent.resetWorking();
                    let message = createErrorMessage('Unable to save product, please contact support');
                    this.router.main_route.layout.toasts.addMessage(message);
                    break;
            }
        });
    };

    /**
     * Fetch product by id
     *
     * @param {string} id - UUID
     * @returns {Promise<void>}
     */
    fetchProductData(id) {
        return new Promise((resolve, reject) => {
            Api.Resources.ProductItems().accept('application/vnd.adg.fx.item-v1+json').retrieve(id).then((entity) => {
                resolve(entity);
            }, (error) => {
                reject(error);
            });
        });
    };

    /**
     * Render child row that shows edit form
     *
     * @param id
     * @returns {Promise<void>}
     */
    async renderChildRow(id) {
        this.state.parent.startWorking();
        let {data: product_data} = await this.fetchProductData(id);
        console.log(product_data);

        let row = this.state.table.getRowById(id);
        let row_data = this.state.table.getRowData(row);
        let units = this.state.units;
        for (let unit of units) {
            if (product_data.unit_id !== unit.id) {
                unit.selected = false;
                continue;
            }
            unit.selected = true;
        }

        // if product contains volume pricing, materials, or additional costs
        // show callout and disable save
        let disable_save = false;
        if (
            product_data.prices.length > 1 ||
            product_data.materials.length > 0 ||
            product_data.additional_costs.length > 0
        ) {
            disable_save = true;
        }

        let callout = null;
        if (
            product_data.prices.length > 1 &&
            (product_data.materials.length > 0 ||
            product_data.additional_costs.length > 0)
        ) {
            callout = `Product contains volume discount pricing and components`;
        } else if (product_data.prices.length > 1) {
            callout = `Product contains volume discount pricing`;
        } else if (product_data.materials.length > 0 ||
            product_data.additional_costs.length > 0)
        {
            callout = `Product contains components`;
        }
        if (callout !== null) {
            callout = `${callout}. Please make changes in the company profile.`;
        }

        let template = edit_tpl({
            disable_save,
            callout,
            name: product_data.name,
            price: parseFloat(product_data.prices[0].price).toFixed(2),
            units,
            categories: row_data.category_list !== null ? row_data.category_list.split(',') : null
        });

        this.state.table.toggleChildRow(row, {
            title: 'Edit Product',
            id: product_data.id,
            content: template,
            disable_save
        });
        this.state.child_row_open = true;
        this.state.parent.resetWorking();
    };

    /**
     * Create the products datatable and apply settings and defaults
     */
    createTable() {
        this.state.table = new Table(this.elem.table, {
            row_edit: true
        })
            .on('scope_change', (scope) => {
                if (this.state.table.getRows().length > 20) {
                    this.state.setup_products = true;
                    this.changeSkipToNext();
                }
                this.state.child_row_open = false;
            })
            .on('row_click', (data) => {
                if (this.state.child_row_open) {
                    return;
                }
                this.renderChildRow(data.id);
            })
            .on('child_row_rendered', (row, content) => {
                this.elem.form = findChild(content, jsSelector('form'));
                this.state.validator = FormValidator.init(this.elem.form)
                    .on('form:submit', () => {
                        this.editProduct(content, row);
                        return false;
                    });

                FormInput.init(findChild(content, jsSelector('price')), {
                    type: NumberInput.Type.CURRENCY,
                    right_align: true,
                    allow_minus: false
                });
            })
            .on('row_save', (row, content) => {
                this.elem.form.trigger('submit');
            })
            .on('row_cancel', (row) => {
                this.state.table.toggleChildRow(row, {}, true);
                this.state.child_row_open = false;
            });

        // set toolbar config
        this.state.table.setToolbar({
            filter: false,
            settings: false
        });

        // set columns config
        this.state.table.setColumns({
            name: {
                label: 'Name',
                width: '50%'
            },
            price: {
                label: 'Price',
                width: '24%',
                value: (data) => {
                    return accounting.formatMoney(data.price);
                }
            },
            unit: {
                label: 'Unit',
                width: '24%'
            }
        });

        this.state.table.setAjax(Api.Resources.ProductItems, (request) => {
            request
                .fields(['id', 'category_list', 'name', 'price', 'unit'])
                .filter('status', Api.Constants.ProductItems.Status.ACTIVE);
        });

        // modify table state
        if (this.state.table_scope) {
            this.state.table.setState(this.state.table_scope);
        }

        // build table
        this.state.table.build();
        this.state.table_loaded = true;
    };

    /**
     * Save current step for company
     *
     * @returns {Promise}
     */
    async saveCompanyStep(type) {
        this.state.parent.startWorking();

        try {
            await $.ajax({
                url: window.fx_url.API + `setup-wizard/company-step`,
                dataType: 'json',
                type: 'PUT',
                contentType: 'application/json',
                data: JSON.stringify({
                    step: type.step
                })
            });
            this.state.parent.resetWorking();
            this.router.navigate(type.route);
        } catch (error) {
            this.state.parent.resetWorking();
            this.setError('Unable to save step, please try again');
        }
    };

    /**
     * Save step when user clicks navigation button
     *
     * @param {string} endpoint
     * @param {boolean} unload
     * @returns {Promise}
     */
    async saveStep(endpoint, unload = false) {
        this.state.parent.startWorking();

        try {
            let response = await $.ajax({
                url: window.fx_url.API + `setup-wizard/${endpoint}`,
                dataType: 'json',
                type: 'PUT',
                contentType: 'application/json',
                data: JSON.stringify({
                    step: this.state.next.step
                })
            });
            if (endpoint === 'next') {
                this.state.setup_products_completed_at = response.productsCompletedAt;
            }
            this.state.parent.resetWorking();
            if (!unload) {
                this.router.navigate(this.state.next.route);
            }
        } catch (error) {
            console.log(error);
            this.state.parent.resetWorking();
            this.setError('Unable to proceed to next step, please try again');
        }
    };

    /**
     * Change skip to next
     */
    changeSkipToNext() {
        this.state.parent.setSkipRoute(null);
        this.state.parent.setNextRoute(this.state.next.route);
        this.state.parent.changeNavState();
    };

    /**
     * Determine if step needs to be saved or skipped
     */
    handleNextClick() {
        let endpoint = this.state.setup_products ? 'next' : 'skip';

        if (endpoint === 'next' && this.state.setup_products_completed_at !== null) {
            this.saveCompanyStep(this.state.next);
            return;
        }
        this.saveStep(endpoint);
    };

    /**
     * Fetch product units
     *
     * @returns {Promise}
     */
    async fetchUnits() {
        let {entities: units} = await Api.Resources.Units()
            .filter('status', Api.Constants.Units.Status.ACTIVE)
            .sort('name', 'asc')
            .all();

        this.state.units = units.map(entity => entity.data);
    };

    /**
     * Refresh page
     *
     * @param {object} request
     */
    refresh(request) {
        if (request.query.update === 'true') {
            this.state.table.draw();
        }
    };

    /**
     * Load page and draw table
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        this.state.parent.scrollTopParent();
        this.state.parent.setBackRoute(this.state.previous.route);
        this.state.parent.setSkipRoute(this.state.next.route);
        this.state.parent.setNextRoute(null);

        await this.fetchUnits();

        if (!this.state.table_loaded) {
            this.createTable();
        } else {
            this.state.table.draw();
        }
        await super.load(request, next);

        let that = this;
        onClickWatcher(this.state.parent.getMainHeader(), jsSelector('button'), function () {
            let $this = $(this);
            //company-step
            if ($this.data('value') === 'next' || $this.data('value') === 'skip') {
                that.handleNextClick();
                return false;
            }
            if ($this.data('value') === 'back') {
                that.saveCompanyStep(that.state.previous);
                return false;
            }
        }, false);

        if (request.data.products) {
            this.state.setup_products = true;
            this.changeSkipToNext();
        }
        if (request.data.products_completed_at !== null) {
            this.state.setup_products_completed_at = request.data.products_completed_at;
        }
    };

    /**
     * Unload page
     *
     * @param {object} request
     * @param {function} next
     */
    async unload(request, next) {
        this.state.parent.setSkipRoute(null);
        onClickDestroy(this.state.parent.getMainHeader());

        if (this.state.setup_products && this.state.setup_products_completed_at === null) {
            this.saveStep('next', true);
        }

        request.data.products = this.state.setup_products;
        request.data.products_completed_at = this.state.setup_products_completed_at;

        this.state.parent.updateSetupData(request.data);
        await super.unload(request, next);
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        this.elem.error = findChild(root, jsSelector('error'));
        this.elem.table = findChild(root, jsSelector('table'));
        this.elem.add_product = findChild(root, jsSelector('add-product'));

        onEvent(this.elem.add_product, 'click', (e) => {
            e.preventDefault();
            this.router.navigate('step.products.add');
            return false;
        });
        $(window).on('resize', (e) => {
            this.state.child_row_open = false;
        });
    };

    /**
     * Render page
     */
    render() {
        return products_tpl({
            brand_name: window.setup_wizard_data.brand_name
        });
    };
}
