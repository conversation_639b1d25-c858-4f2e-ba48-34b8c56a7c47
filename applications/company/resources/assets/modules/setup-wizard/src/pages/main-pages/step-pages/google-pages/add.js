'use strict';

const Modal = require('@ca-package/router/src/modal');
const {createSuccessMessage} = require('@cas-notification-toast-js/message/success');

export class CalendarAdd extends Modal {
    /**
     * Get and cache modal
     *
     * @returns {module:Modal.Base}
     */
    get modal() {
        if (this.state.modal === undefined) {
            let modal = require('../../../../modals/google-calendar/calendar_add');
            this.state.modal = new modal(this);
        }
        return this.state.modal;
    };

    /**
     * Open add modal with promise
     *
     * @returns {Promise<undefined>}
     */
    openModal() {
        return new Promise((resolve, reject) => {
            return this.modal.open({resolve, reject});
        });
    };

    /**
     * Load modal
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async load(request, next) {
        await super.load(request, next);
        this.openModal().then(result => {
            let query = null;
            if (result !== null) {
                this.router.main_route.layout.toasts.addMessage(createSuccessMessage('Calendar has been successfully added'));
                query = {update: 'true'};
            }
            this.router.navigate('step.google_calendar', {}, query);
        });
    };

    /**
     * Unload modal
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async unload(request, next) {
        this.modal.externalClose();
        await super.unload(request, next);
    };
}
