'use strict';

const Api = require('@ca-package/api');
const Page = require('@ca-package/router/src/page');
const {findChild, jsSelector, onEvent} = require("@ca-package/dom");

const {createSuccessMessage} = require('@cas-notification-toast-js/message/success');
const {createErrorMessage} = require('@cas-notification-toast-js/message/error');
const FormInput = require('@ca-submodule/form-input');
const NumberInput = require('@ca-submodule/form-input/src/number');
FormInput.use(require('@ca-submodule/form-input/src/button_group'));
FormInput.use(NumberInput);
const Tooltip = require('@ca-submodule/tooltip');

const Number = require('@cac-js/utils/number');
require('@cac-js/handlebars-helpers/each_in_map');
require('@cac-js/handlebars-helpers/if_eq');

const FormValidator = require("@cas-validator-js");

const {initSelectPlaceholder} = require('@cac-js/utils/select_placeholder');

const bid_defaults_tpl = require('@cam-company-profile-tpl/pages/main-pages/settings-pages/bid-defaults-pages/edit.hbs');
const installment_row_tpl = require('@cam-company-profile-tpl/pages/main-pages/settings-pages/bid-defaults-pages/bid-defaults-components/installment_edit_row.hbs');

const DueOptions = {
    1: 'At Bid Acceptance',
    2: 'After Bid Acceptance',
    3: 'Before Project Start',
    4: 'After Project Start',
    5: 'Before Project Completion',
    6: 'After Project Completion',
    7: 'At Project Completion',
    8: 'At Closing'
};

const AmountTypes = {
    CURRENCY: 1,
    PERCENTAGE: 2
};

class Edit extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent) {
        super(router, name, parent);
        Object.assign(this.state, {
            settings: parent.getParentByName('settings'),
            bid_installment_items: {},
            amount_types: new Map([
                [AmountTypes.CURRENCY, {
                    label: '$',
                    input_type: NumberInput.Type.CURRENCY
                }],
                [AmountTypes.PERCENTAGE, {
                    label: '%',
                    input_type: NumberInput.Type.PERCENTAGE
                }]
            ])
        });
    };

    /**
     * Prepare entity to save
     *
     * @returns {object}
     */
    buildEntity() {
        let installments = [];
        for (let item in this.state.bid_installment_items) {
            let $item = this.state.bid_installment_items[item],
                amount = Number.ofInput($item.fields.amount.val()),
                amount_type = parseFloat($item.fields.amount_type.val());

            if (amount_type === AmountTypes.PERCENTAGE) {
                amount = amount.div(100);
            }
            installments.push({
                name: $item.fields.name.val(),
                due_time_frame: parseFloat($item.fields.due_time_frame.val()),
                amount_type,
                amount
            });
        }

        return {
            settings: {
                payment_term_one_time_due_time_frame: parseFloat(this.state.validator.getInputElem('payment_term_one_time_due_time_frame').val()),
                payment_term_installments: installments
            }
        };
    };

    /**
     * Save settings data to server
     */
    save() {
        this.state.settings.showLoader();
        let data = this.buildEntity();

        Api.Resources.Companies().partialUpdate('current', data).then(({data}) => {
            setTimeout(() => {
                this.state.settings.hideLoader();
                let message = createSuccessMessage(`Company bid defaults saved successfully`);
                this.router.main_route.layout.toasts.addMessage(message);
                this.router.navigate('settings.bid_defaults.details');
            }, 2000);
        }, (error, response) => {
            switch (response.statusCode()) {
                case 422:
                    let item_errors = response.data().errors;
                    // for (let item in item_errors) {
                    //     if (this.state.field[item] === undefined) {
                    //         continue;
                    //     }
                    //     this.state.field[item].addError('fx-' + item, {message: item_errors[item]});
                    // }
                    this.state.settings.hideLoader();
                    break;
                default:
                    let message = createErrorMessage('Unable to save bid defaults, please contact support');
                    this.router.main_route.layout.toasts.addMessage(message);
                    break;
            }
        });
    };

    /**
     * Remove installments row
     *
     * @param {number} index
     */
    removeInstallmentsRow(index) {
        this.state.bid_installment_items[index].elem.remove();
        delete this.state.bid_installment_items[index];
        if (Object.keys(this.state.bid_installment_items).length === 0) {
            this.addInstallmentRow();
        }
    };

    /**
     * Add installment row
     *
     * @param {Object} data
     */
    addInstallmentRow(data = null) {
        if (data !== null && data.amount_type === AmountTypes.PERCENTAGE) {
            data.amount = Number.of(data.amount).mul(100).toPrecision();
        }
        let index = Object.keys(this.state.bid_installment_items).length,
            row = $(installment_row_tpl({
            index,
            name: data === null ? '' : data.name,
            amount_types: this.state.amount_types,
            amount_type: data === null ? 0 : data.amount_type,
            amount: data === null ? '' : data.amount,
            due_time_frames: DueOptions
        }));

        let name = findChild(row, jsSelector('name')),
            due_time_frame = findChild(row, jsSelector('due-time-frame')),
            amount_type = findChild(row, jsSelector('amount-type')),
            amount = findChild(row, jsSelector('amount')),
            remove_row = findChild(row, jsSelector('remove'));
        initSelectPlaceholder(due_time_frame);

        let default_amount_type = AmountTypes.CURRENCY;
        if (data !== null) {
            due_time_frame.val(data.due_time_frame).trigger('change');
            default_amount_type = data.amount_type;
        }
        FormInput.init(amount_type);
        FormInput.init(amount, {
            type: this.state.amount_types.get(default_amount_type).input_type,
            right_align: true
        });

        onEvent(amount_type, 'change', (e) => {
            let amount_type_id = parseInt(amount_type.val());
            amount.data('fx_input').setType(this.state.amount_types.get(amount_type_id).input_type);
            amount.val('');
        });

        let that = this;
        onEvent(remove_row, 'click', function (e) {
            e.preventDefault();
            let index = $(this).data('index');
            that.removeInstallmentsRow(index);
            return false;
        });

        this.state.bid_installment_items[index] = {
            elem: row,
            fields: {
                name,
                due_time_frame,
                amount_type,
                amount
            }
        };
        this.elem.bid_payment_term_installment_table.append(row);
    };

    /**
     * Populate data on page
     *
     * @param {Object} data
     */
    populate(data) {
        this.state.validator.getInputElem('payment_term_one_time_due_time_frame').val(data.settings.payment_term_one_time_due_time_frame);
        this.clearTable();
        for (let item of data.settings.payment_term_installments) {
            this.addInstallmentRow(item);
        }
    };

    /**
     * Fetch settings data from server
     */
    async fetchData() {
        try {
            let {data: entity} = await Api.Resources.Companies().
            fields(['id']).relations({
                'settings': {}
            }).retrieve('current');
            this.populate(entity);
        } catch (e) {}
    };

    /**
     * Refresh page
     *
     * @param {object} request
     */
    async refresh(request) {
        await this.fetchData();
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        await super.load(request, next);
        this.state.settings.setEditMode(true);
        this.state.settings.showLoader();
        await this.fetchData();
        this.state.settings.hideLoader();
    };

    /**
     * Clear table and reset object
     */
    clearTable() {
        for (let item in this.state.bid_installment_items) {
            let $item = this.state.bid_installment_items[item].elem;
            $item.remove();
        }
        this.state.bid_installment_items = {};
    };

    /**
     * Unload page
     *
     * @param {object} request
     * @param {function} next
     */
    async unload(request, next) {
        this.state.validator.getInputElem('payment_term_one_time_due_time_frame').val('');

        this.clearTable();
        this.state.validator.reset();
        this.elem.root.scrollTop(0);

        this.clearError();
        this.state.settings.setEditMode(false);
        await super.unload(request, next);
    };

    /**
     * Set message and show error container
     *
     * @param {string} message
     */
    setError(message) {
        this.elem.form.scrollTop(0);
        this.elem.error.text(message).addClass('t-show');
    };

    /**
     * Clear and hide error container
     */
    clearError() {
        this.elem.error.text('').removeClass('t-show');
    };

    /**
     * Initialize form to use on submit
     */
    initForm() {
        this.state.validator = FormValidator.create(this.elem.form, {
            payment_term_one_time_due_time_frame: {
                required: true
            }
        }, {
            validate_event: true,
            error_event: true
        })
            .on('submit', () => this.save())
            .on('validate', () => {})
            .on('error', () => this.setError('Please review errors below'));
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        Tooltip.initAll(root);

        this.elem.form = findChild(root, jsSelector('form'));
        this.elem.bid_payment_term_installment_table = findChild(root, jsSelector('bid-payment-term-installment-table'));
        this.elem.error = findChild(root, jsSelector('error'));
        this.elem.save = findChild(root, jsSelector('save'));
        this.elem.add_bid_payment_term_installment = findChild(root, jsSelector('add-bid-payment-term-installment'));

        this.initForm();

        onEvent(this.elem.add_bid_payment_term_installment, 'click', (e) => {
            e.preventDefault();
            this.addInstallmentRow();
            return false;
        });

        onEvent(this.elem.save, 'click', (e) => {
            e.preventDefault();
            this.elem.form.trigger('submit');
            return false;
        });
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return bid_defaults_tpl({
            title: 'Edit Bid Defaults',
            cancel_route: 'settings.bid_defaults.details',
            due_time_frames: DueOptions
        });
    };
}

module.exports = Edit;