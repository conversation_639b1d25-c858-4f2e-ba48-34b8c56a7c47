'use strict';

const autosize = require('autosize');
const cloneDeep = require('lodash/cloneDeep');
const isEqual = require('lodash/isEqual');
const keyBy = require('lodash/keyBy');

const Api = require('@ca-package/api');
const FormValidator = require('@ca-submodule/validator');
const Page = require('@ca-package/router/src/page');

require('@cac-js/handlebars-helpers/nl2br');

const {createSuccessMessage} = require('@cas-notification-toast-js/message/success');
const {createErrorMessage} = require('@cas-notification-toast-js/message/error');
const FormInput = require('@ca-submodule/form-input');
const NumberInput = require('@ca-submodule/form-input/src/number');
FormInput.use(require('@ca-submodule/form-input/src/dynamic_dropdown'));
FormInput.use(require('@ca-submodule/form-input/src/nested_dropdown'));
FormInput.use(require('@ca-submodule/form-input/src/wysiwyg'));
FormInput.use(require('@ca-submodule/form-input/src/switch'));
FormInput.use(NumberInput);
const Tooltip = require('@ca-submodule/tooltip');

const create_update_tpl = require('@cam-company-profile-tpl/pages/main-pages/forms-pages/items-pages/create_update.hbs');
const template_tpl = require('@cam-company-profile-tpl/pages/main-pages/forms-pages/items-pages/create-update-components/template.hbs');
const product_list_tpl = require('@cam-company-profile-tpl/pages/main-pages/forms-pages/items-pages/create-update-components/fields/product_list.hbs');
const product_item_tpl = require('@cam-company-profile-tpl/pages/main-pages/forms-pages/items-pages/create-update-components/dependencies/product_item.hbs');
const meta_multiline_string_tpl = require('@cam-company-profile-tpl/pages/main-pages/forms-pages/items-pages/create-update-components/meta/multiline_string.hbs');
const meta_number_tpl = require('@cam-company-profile-tpl/pages/main-pages/forms-pages/items-pages/create-update-components/meta/number.hbs');
const meta_string_tpl = require('@cam-company-profile-tpl/pages/main-pages/forms-pages/items-pages/create-update-components/meta/string.hbs');
const meta_boolean_tpl = require('@cam-company-profile-tpl/pages/main-pages/forms-pages/items-pages/create-update-components/meta/boolean.hbs');

const FormTypes = {
    BID: 1
};
const FieldTypes = {
    PRODUCT_LIST: 7
};
const FieldProductItemTypes = {
    CATEGORY: 12,
    ITEM: 13
};
const FieldProductActions = {
    ADD: 1,
    REMOVE: 2
};
const DependencyTypes = {
    PRODUCT_ITEM: 1
};
const MetaValueTypes = {
    STRING: 1,
    BOOL: 2,
    INT: 3,
    FLOAT: 4,
    MULTILINE_STRING: 5
};
const FormTypeOptions = {
    [FormTypes.BID]: [
        {id: 'option-hide-name', key: 'is_form_name_hidden'},
        {id: 'option-enabled', key: 'is_hidden_from_list', invert: true},
        {id: 'option-show-on-bid-document', key: 'is_hidden_from_bid', invert: true},
        {id: 'option-show-on-job-document', key: 'is_hidden_from_scope_of_work', invert: true},
        {id: 'option-auto-add-bid', key: 'is_bid_default', group: 'auto-add'},
        {id: 'option-auto-add-bid-section', key: 'is_section_default', group: 'auto-add'}
    ]
};

/**
 * @typedef {object} Structure
 * @property {number} type
 * @property {array} template_overrides
 * @property {array} field_overrides
 * @property {array} dependencies
 * @property {array} meta
 */

/**
 * @typedef {object} Template
 * @property {object} source
 * @property {string} source.id
 * @property {string|null} source.content
 * @property {object} elem
 * @property {jQuery} elem.root
 * @property {jQuery} elem.input
 * @property {module:FormInput.Wysiwyg} input
 * @property {object} parsley
 * @property {object} payload
 * @property {string|null} payload.content
 */

/**
 * @typedef {object} Field
 * @property {object} elem
 * @property {jQuery} elem.root
 * @property {jQuery} elem.input
 * @property {object} input
 * @property {object} source
 * @property {string} source.id
 * @property {number} source.type
 * @property {object|null} config
 * @property {string} config.default_product_category_name
 * @property {string} config.product_category_create_instruction
 * @property {object} payload
 */

/**
 * @typedef {object} Dependency
 * @property {object} elem
 * @property {jQuery} elem.root
 * @property {jQuery} elem.input
 * @property {object} input
 * @property {object} source
 * @property {string} source.id
 * @property {number} source.type
 * @property {object|null} config
 * @property {string} config.default_product_item_name
 * @property {string} config.product_item_create_instruction
 * @property {object} payload
 */

/**
 * @typedef {object} Meta
 * @property {object} elem
 * @property {jQuery} elem.root
 * @property {jQuery} elem.input
 * @property {object} input
 * @property {object} source
 * @property {string} source.id
 * @property {number} source.value_type
 * @property {object} payload
 */

/**
 * @typedef {object} Option
 * @property {object} data
 * @property {jQuery} elem
 * @property {object} input
 */

class CreateUpdate extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);
        Object.assign(this.state, {
            forms: parent.getParentByName('forms'),
            is_update: name === 'update',
            is_duplicate: name === 'duplicate',
            built: false,
            current_info: null,
            current_categories: null,
            current_relations: null,
            raw_form: null,
            form_categories: null,
            product_categories: null,
            templates: {},
            fields: {},
            dependencies: {},
            meta: {},
            options: {},
            form_validated: false,
            preview_mode: false
        });
        this.state.namespace = this.state.is_update ? 'e' : (this.state.is_duplicate ? 'd' : 'a');
    };

    /**
     * Get namespace for page
     *
     * @readonly
     *
     * @returns {string}
     */
    get namespace() {
        return this.state.namespace;
    };

    /**
     * Get structure
     *
     * @readonly
     *
     * @returns {null|Structure}
     */
    get structure() {
        return this.state.raw_form.structure;
    };

    /**
     * Create template input
     *
     * @param {{label: string, description: string, source: {id: string, content: string|null}, config: object|null}} template
     */
    async createTemplate(template) {
        let elem = $(template_tpl({
                label: template.label,
                description: template.description
            })),
            info = {
                source: template.source,
                config: template.config || {},
                elem: {
                    root: elem,
                    input: elem.fxFind('input')
                }
            };
        this.elem.overrides.append(elem);
        info.input = FormInput.init(info.elem.input, {
            preset: 'simple',
            height: 200,
            blocks: true,
            remove_empty_paragraphs: true
        });
        await info.input.promise;
        this.state.templates[template.source.id] = info;
    };

    /**
     * Populate template with data
     *
     * @param {Template} template
     * @param {object} data
     * @param {boolean} [duplicate=true]
     * @returns {Promise<void>}
     */
    async populateTemplate(template, data, duplicate = false) {
        if (duplicate) {
            delete data.id;
        }
        template.payload = cloneDeep(data);
        template.input.setContent(template.payload.content);
    };

    /**
     * Get payload for template
     *
     * @param {Template} template
     * @returns {object}
     */
    getTemplatePayload(template) {
        return Object.assign({}, template.payload, {
            content: template.elem.input.val() || null
        });
    };

    /**
     * Remove template from UI
     *
     * @param {Template} template
     */
    removeTemplate(template) {
        template.input.destroy();
        template.elem.root.remove();
    };

    /**
     * Get and cache product categories list
     *
     * @returns {Promise<array>}
     */
    async getProductCategories() {
        if (this.state.product_categories === null) {
            let {entities: categories} = await Api.Resources.ProductCategories()
                .sort('name', 'asc')
                .accept('application/vnd.adg.fx.list-v1+json')
                .all();
            this.state.product_categories = categories.map(category => category.data);
        }
        return this.state.product_categories;
    };

    /**
     * Get info for specific product category
     *
     * @param {string} id
     * @returns {Promise<{items_count: number}>}
     */
    async getProductCategory(id) {
        let {data: category} = await Api.Resources.ProductCategories()
            .fields(['items_count'])
            .retrieve(id);
        return category;
    };

    /**
     * Create field input
     *
     * @param {{label: string, description: string, source: {id: string, type: number}, config: object|null}} field
     */
    async createField(field) {
        let elem,
            info = {
                source: field.source,
                config: field.config || {},
                elem: {}
            };
        switch (field.source.type) {
            case FieldTypes.PRODUCT_LIST:
                elem = $(product_list_tpl({
                    id: field.source.id,
                    label: field.label,
                    description: field.description
                }));
                info.elem.input = elem.fxFind('input');
                info.elem.category = elem.fxFind('category-info');
                info.elem.product_count = info.elem.category.fxFind('product-count');
                this.elem.overrides.append(elem);
                info.input = FormInput.init(info.elem.input, {
                    data_provider: this.getProductCategories.bind(this),
                    option_handler: (data, option) => {
                        option({
                            id: data.id,
                            text: data.name
                        }, data.categories);
                    },
                    placeholder: 'Choose a Product Category',
                    closeOnSelect: true
                });
                await info.input.promise;
                info.parsley = info.elem.input.parsley({
                    required: true,
                    requiredMessage: 'Required'
                });
                info.elem.input.on('dropdown:change', () => {
                    info.elem.category.hide();
                    let value = info.elem.input.val();
                    if (value !== '') {
                        this.getProductCategory(value).then(data => {
                            info.elem.product_count.text(`${data.items_count} product${data.items_count === 1 ? '' : 's'}`);
                            info.elem.category.show();
                        });
                    }
                    if (!this.state.form_validated) {
                        return;
                    }
                    info.parsley.validate();
                }).trigger('dropdown:change');
                break;
            default:
                throw new Error(`Unsupported override field type: ${field.source.type}`);
        }
        info.elem.root = elem;
        this.state.fields[field.source.id] = info;
    };

    /**
     * Get field info by id
     * @param {string} id
     * @returns {Field}
     */
    getField(id) {
        let field = this.state.fields[id];
        if (field === undefined) {
            throw new Error(`Unable to find field with id: ${id}`);
        }
        return field;
    };

    /**
     * Set value for field
     *
     * @param {Field} field
     * @param {*} value
     */
    setFieldValue(field, value) {
        field.input.setValue(value);
        field.elem.input.trigger('dropdown:change');
    };

    /**
     * Populate field with data
     *
     * @param {Field} field
     * @param {object} data
     * @param {boolean} [duplicate=false]
     * @returns {Promise<void>}
     */
    async populateField(field, data, duplicate = false) {
        if (duplicate) {
            delete data.id;
        }
        field.payload = cloneDeep(data);
        switch (field.payload.type) {
            case FieldTypes.PRODUCT_LIST:
                if (duplicate) {
                    delete field.payload.products[0].id;
                }
                let item_id = field.payload.products[0].item_id;
                if (item_id === null) {
                    break;
                }
                this.setFieldValue(field, item_id);
                break;
            default:
                throw new Error(`Unsupported field type: ${field.payload.type}`);
        }
    };

    /**
     * Get payload for field
     *
     * @param {Field} field
     * @returns {object}
     */
    getFieldPayload(field) {
        let payload = cloneDeep(field.payload);
        switch (field.source.type) {
            case FieldTypes.PRODUCT_LIST:
                Object.assign(payload.products[0], {
                    item_id: field.elem.input.val() || null
                });
                break;
        }
        return payload;
    };

    /**
     * Remove field from UI
     *
     * @param {Field} field
     */
    removeField(field) {
        field.parsley.destroy();
        field.input.destroy();
        field.elem.root.remove();
    };

    /**
     * Create dependency input
     *
     * @param {{id: string, label: string, description: string, type: number, config: object|null}} dependency
     */
    async createDependency(dependency) {
        let elem,
            info = {
                source: dependency,
                config: dependency.config || {},
                elem: {}
            };
        switch (dependency.type) {
            case DependencyTypes.PRODUCT_ITEM:
                elem = $(product_item_tpl({
                    id: dependency.id,
                    label: dependency.label,
                    description: dependency.description
                }));
                info.elem.input = elem.fxFind('input');
                this.elem.dependencies.append(elem);
                info.input = FormInput.init(info.elem.input, {
                    placeholder: 'Select a Product',
                    closeOnSelect: true,
                    request: data => {
                        let request = Api.Resources.ProductItems()
                            .fields(['id', 'name'])
                            .filter('status', Api.Constants.ProductItems.Status.ACTIVE)
                            .page(data.page).perPage(15);
                        if (data.term) {
                            request.search(data.term);
                        }
                        return request.all();
                    },
                    response: (collection, formatter) => {
                        let results = [];
                        collection.entities.forEach((product) => {
                            results.push({
                                id: product.get('id'),
                                text: product.get('name')
                            });
                        });
                        return formatter(results, collection.response.meta('pagination.next_page') !== null);
                    }
                });
                info.parsley = info.elem.input.parsley({
                    required: true,
                    requiredMessage: 'Required'
                });
                info.elem.input.on('dropdown:change', () => {
                    if (!this.state.form_validated) {
                        return;
                    }
                    info.parsley.validate();
                });
                break;
            default:
                throw new Error(`Unsupported dependency type: ${dependency.type}`);
        }
        info.elem.root = elem;
        this.state.dependencies[dependency.id] = info;
    };

    /**
     * Get dependency by id
     * @param {string} id
     * @returns {Dependency}
     */
    getDependency(id) {
        let dependency = this.state.dependencies[id];
        if (dependency === undefined) {
            throw new Error(`Unable to find dependency with id: ${id}`);
        }
        return dependency;
    };

    /**
     * Get product item by id
     *
     * @param {string} id
     * @returns {Promise<{id: string, name: string}>}
     */
    async getProductItem(id) {
        let {data: product} = await Api.Resources.ProductItems()
            .fields(['id', 'name'])
            .retrieve(id);
        return product;
    };

    /**
     * Set value of dependency
     *
     * @param {Dependency} dependency
     * @param {object} option
     * @param {boolean} [reset=false] - Determines of field is fully reset
     */
    setDependencyValue(dependency, option, reset = false) {
        if (reset) {
            dependency.input.reset();
        }
        dependency.input.setDefaultOption(option);
    };

    /**
     * Populate dependency with data
     *
     * @param {Dependency} dependency
     * @param {object} data
     * @param {boolean} [duplicate=false]
     * @returns {Promise<void>}
     */
    async populateDependency(dependency, data, duplicate = false) {
        if (duplicate) {
            delete data.id;
        }
        dependency.payload = cloneDeep(data);
        if (dependency.payload.item_id === null) {
            return;
        }
        let product = await this.getProductItem(dependency.payload.item_id);
        this.setDependencyValue(dependency, {
            id: product.id,
            text: product.name
        });
    };

    /**
     * Get payload for dependency
     *
     * @param {Dependency} dependency
     * @returns {object}
     */
    getDependencyPayload(dependency) {
        let payload = cloneDeep(dependency.payload);
        switch (payload.type) {
            case DependencyTypes.PRODUCT_ITEM:
                Object.assign(payload, {
                    item_id: dependency.elem.input.val() || null
                });
                break;
            default:
                throw new Error(`Unsupported dependency type: ${payload.type}`);
        }
        return payload;
    };

    /**
     * Remove dependency from UI
     *
     * @param {Dependency} dependency
     */
    removeDependency(dependency) {
        dependency.parsley.destroy();
        dependency.input.destroy();
        dependency.elem.root.remove();
    };

    /**
     * Create meta input
     *
     * @param {{id: string, label: string, description: string, value_type: number}} meta
     */
    async createMeta(meta) {
        let elem,
            info = {
                source: meta,
                elem: {}
            };
        let template = {
            [MetaValueTypes.INT]: meta_number_tpl,
            [MetaValueTypes.FLOAT]: meta_number_tpl,
            [MetaValueTypes.STRING]: meta_string_tpl,
            [MetaValueTypes.MULTILINE_STRING]: meta_multiline_string_tpl
        };
        switch (meta.value_type) {
            case MetaValueTypes.INT:
            case MetaValueTypes.FLOAT:
            case MetaValueTypes.STRING:
            case MetaValueTypes.MULTILINE_STRING:
                elem = $(template[meta.value_type]({
                    label: meta.label,
                    description: meta.description
                }));
                info.elem.input = elem.fxFind('input');
                this.elem.meta.append(elem);
                if ([MetaValueTypes.INT, MetaValueTypes.FLOAT].indexOf(meta.value_type) !== -1) {
                    info.input = FormInput.init(info.elem.input, {
                        type: meta.value_type === MetaValueTypes.INT ? NumberInput.Type.INT : NumberInput.Type.FLOAT
                    });
                }
                info.parsley = info.elem.input.parsley({
                    required: true,
                    requiredMessage: 'Required'
                });
                break;
            case MetaValueTypes.BOOL:
                elem = $(meta_boolean_tpl({
                    label: meta.label,
                    description: meta.description
                }));
                info.elem.input = elem.fxFind('input');
                this.elem.meta.append(elem);
                info.input = FormInput.init(info.elem.input);
                break;
            default:
                throw new Error(`Unsupported meta value type: ${meta.value_type}`);
        }
        info.elem.root = elem;
        this.state.meta[meta.id] = info;
    };

    /**
     * Populate meta with data
     *
     * @param {Meta} meta
     * @param {object} data
     * @param {boolean} [duplicate=false]
     * @returns {Promise<void>}
     */
    async populateMeta(meta, data, duplicate = false) {
        if (duplicate) {
            delete data.id;
        }
        meta.payload = cloneDeep(data);
        switch (meta.source.value_type) {
            case MetaValueTypes.INT:
            case MetaValueTypes.FLOAT:
            case MetaValueTypes.STRING:
            case MetaValueTypes.MULTILINE_STRING:
                meta.elem.input.val(meta.payload.value);
                if (meta.source.value_type === MetaValueTypes.MULTILINE_STRING) {
                    autosize(meta.elem.input);
                }
                break;
            case MetaValueTypes.BOOL:
                meta.elem.input.prop('checked', meta.payload.value);
                break;
        }
    };

    /**
     * Get payload for meta
     *
     * @param {Meta} meta
     * @returns {object}
     */
    getMetaPayload(meta) {
        let value = null;
        switch (meta.source.value_type) {
            case MetaValueTypes.INT:
            case MetaValueTypes.FLOAT:
            case MetaValueTypes.STRING:
            case MetaValueTypes.MULTILINE_STRING:
                value = meta.elem.input.val().trim();
                if (meta.source.value_type === MetaValueTypes.INT) {
                    value = parseInt(value);
                    break;
                }
                if (meta.source.value_type === MetaValueTypes.FLOAT) {
                    value = parseFloat(value);
                    break;
                }
                break;
            case MetaValueTypes.BOOL:
                value = meta.elem.input.is(':checked');
                break;
        }
        return Object.assign(cloneDeep(meta.payload), {
            value
        });
    };

    /**
     * Remove meta from UI
     *
     * @param {Meta} meta
     */
    removeMeta(meta) {
        if (meta.parsley) {
            meta.parsley.destroy();
        }
        if (meta.input) {
            meta.input.destroy();
        }
        meta.elem.root.remove();
    };

    /**
     * Setup options for specified form type
     *
     * @param {number} type
     */
    setupOptions(type) {
        let options = FormTypeOptions[type],
            groups = {};
        if (options !== undefined) {
            for (let option of options) {
                let info = {
                    data: option,
                    elem: this.elem.form.fxFind(option.id)
                };
                info.input = FormInput.init(info.elem);
                this.state.options[option.id] = info;
                if (option.group) {
                    if (groups[option.group] === undefined) {
                        groups[option.group] = [];
                    }
                    groups[option.group].push(option.id);
                    info.elem.fxEvent('change', () => {
                        for (let option_id of groups[option.group]) {
                            if (option_id === option.id) {
                                continue;
                            }
                            this.state.options[option_id].elem.prop('checked', false);
                        }
                    });
                }
            }
        }
    };

    /**
     * Populate options
     *
     * @param {object} data
     * @returns {Promise<void>}
     */
    async populateOptions(data) {
        for (let option_id of Object.keys(this.state.options)) {
            let option = this.state.options[option_id],
                value = data[option.data.key];
            if (option.data.invert) {
                value = !value;
            }
            option.elem.prop('checked', value);
        }
    };

    /**
     * Clear option
     *
     * @param option
     */
    clearOption(option) {
        option.input.destroy();
        if (option.data.group) {
            option.elem.fxEventDestroy('change');
        }
    };

    /**
     * Get all form categories and cache
     *
     * @returns {Promise<array>}
     */
    async getFormCategories() {
        if (this.state.form_categories === null) {
            let {entities: categories} = await Api.Resources.CompanyFormCategories()
                // filter type based on form type
                .filter('type', Api.Constants.CompanyFormCategories.Type.BID)
                .sort('name', 'asc')
                .accept('application/vnd.adg.fx.list-v1+json')
                .all();
            this.state.form_categories = categories.map(category => category.data);
        }
        return this.state.form_categories;
    };

    /**
     * Get and cache preview modal
     *
     * @returns {Preview}
     */
    get preview_modal() {
        if (this.state.preview_modal === undefined) {
            let modal = require('../../../../modals/form/preview');
            this.state.preview_modal = new modal(this);
        }
        return this.state.preview_modal;
    };

    /**
     * Build form layout from structure
     *
     * @param {Structure} structure
     */
    async buildForm(structure) {
        this.elem.input = {};
        for (let name of ['name', 'categories']) {
            this.elem.input[name] = this.elem.form.fxFind(name);
        }

        this.state.field = {};
        this.state.field.categories = FormInput.init(this.elem.input.categories, {
            data_provider: this.getFormCategories.bind(this),
            option_handler: (data, option) => {
                option({
                    id: data.id,
                    text: data.name
                }, data.categories);
            },
            placeholder: 'Choose Filters to Organize Your Form',
            closeOnSelect: true
        });

        this.state.form = FormValidator.init(this.elem.form)
            .on('form:validate', () => {
                this.state.form_validated = true;
            })
            .on('form:submit', () => {
                if (this.state.preview_mode) {
                    this.preview_modal.open();
                    this.state.preview_mode = false;
                    return false;
                }
                this.save();
                return false;
            });

        let fields = {
            name: {
                required: true,
                maxlength: 100,
                maxlengthMessage: 'Invalid length - 100 chars. max'
            }
        };
        for (let name of Object.keys(fields)) {
            if (fields[name].requiredMessage === undefined) {
                fields[name].requiredMessage = 'Required';
            }
            this.state.field[name] = this.elem.input[name].parsley(fields[name]);
        }

        let types = [
            ['template_overrides', 'createTemplate'],
            ['field_overrides', 'createField'],
            ['dependencies', 'createDependency'],
            ['meta', 'createMeta']
        ];
        let promises = [this.state.field.categories.promise];
        for (let [key, method] of types) {
            if (structure[key].length === 0) {
                continue;
            }
            structure[key].forEach(data => promises.push(this[method](data)));
        }
        if (promises.length > 0) {
            await Promise.all(promises);
        }

        this.setupOptions(structure.type);
        if (structure.type === FormTypes.BID) {
            this.elem.hide_name_wrapper.show();
            this.elem.bid_settings.show();
        }

        this.state.built = true;
    };

    /**
     * Get default payload from system form
     *
     * @param {string} name
     * @param {object} item
     * @param {Structure} structure
     * @param {number} source_form_type
     * @param {string} source_form_id
     * @returns {object}
     */
    getDefaultPayload({name, item, structure}, source_form_type, source_form_id) {
        let payload = {
            name,
            item,
            structure,
            type: structure.type,
            form_item_id: structure.id,
            categories: [],
            templates: [],
            fields: [],
            dependencies: [],
            meta: [],
            source_form_type,
            source_form_id
        };
        if (structure.template_overrides.length > 0) {
            for (let {source: {id, type, content}} of structure.template_overrides) {
                payload.templates.push({
                    form_item_group_template_id: id,
                    type,
                    content
                });
            }
        }
        if (structure.field_overrides.length > 0) {
            for (let field of structure.field_overrides) {
                let field_payload = Object.assign(cloneDeep(field.source), {
                    form_item_group_field_id: field.source.id
                });
                delete field_payload.id;
                switch (field.source.type) {
                    case FieldTypes.PRODUCT_LIST:
                        field_payload.products = [{
                            item_type: FieldProductItemTypes.CATEGORY,
                            item_id: null,
                            action: FieldProductActions.ADD
                        }];
                        break;
                    default:
                        throw new Error(`Unsupported field type: ${field.source.type}`);
                }
                payload.fields.push(field_payload);
            }
        }
        if (structure.dependencies.length > 0) {
            for (let {id, type} of structure.dependencies) {
                payload.dependencies.push({
                    form_item_dependency_id: id,
                    type,
                    item_id: null
                });
            }
        }
        if (structure.meta.length > 0) {
            for (let {id, value_type, value} of structure.meta) {
                payload.meta.push({
                    form_item_meta_id: id,
                    value_type,
                    value
                });
            }
        }
        return payload;
    };

    /**
     * Populate form using payload
     *
     * @param {object} payload
     * @param {boolean} [duplicate=false]
     * @returns {Promise<void>}
     */
    async populate(payload, duplicate = false) {
        if (duplicate) {
            payload.source_form_type = Api.Constants.CompanyFormItems.SourceFormType.COMPANY;
            payload.source_form_id = payload.id;
            delete payload.id;
        }
        delete payload.structure;
        this.state.current_info = payload;
        this.state.current_relations = {};
        this.elem.input.name.val(payload.name);
        this.state.current_categories = [];
        if (payload.categories.length > 0) {
            this.elem.input.categories.val(payload.categories).trigger('change');
            this.state.current_categories = payload.categories.sort();
        }
        delete payload.categories;
        let types = [
                ['templates', 'form_item_group_template_id', 'populateTemplate'],
                ['fields', 'form_item_group_field_id', 'populateField'],
                ['dependencies', 'form_item_dependency_id', 'populateDependency'],
                ['meta', 'form_item_meta_id', 'populateMeta']
            ],
            promises = [];
        for (let [type, key, method] of types) {
            let items = keyBy(payload[type], key);
            delete payload[type];
            this.state.current_relations[type] = [];
            for (let id of Object.keys(this.state[type])) {
                let data = items[id];
                if (data === undefined) {
                    throw new Error(`Value not found for type ${type} and id ${id}`);
                }
                promises.push(this[method](this.state[type][id], data, duplicate));
                this.state.current_relations[type].push(data);
            }
        }
        promises.push(this.populateOptions(payload.item));
        if (promises.length > 0) {
            await Promise.all(promises);
        }
    };

    /**
     * Load system form item from server by id
     *
     * Will build form using returned structure and populate with default data.
     *
     * @param {string} id
     * @returns {Promise<void>}
     */
    async loadSystemFormItem(id) {
        let {data} = await Api.Resources.SystemFormItems()
            .accept('application/vnd.adg.fx.setup-v1+json')
            .retrieve(id);
        data = this.getDefaultPayload(data, Api.Constants.CompanyFormItems.SourceFormType.SYSTEM, data.id);
        this.state.raw_form = cloneDeep(data);
        await this.buildForm(data.structure);
        if (!(await this.restoreSnapshot())) {
            await this.populate(data);
        }
    };

    /**
     * Load company form item from server by id
     *
     * Will build form using returned structure and populate with data.
     *
     * @param {string} id
     * @returns {Promise<void>}
     */
    async loadCompanyFormItem(id) {
        let {data} = await Api.Resources.CompanyFormItems()
            .accept('application/vnd.adg.fx.setup-v1+json')
            .retrieve(id);
        this.state.raw_form = cloneDeep(data);
        await this.buildForm(data.structure);
        if (!(await this.restoreSnapshot())) {
            await this.populate(data, this.state.is_duplicate);
        }
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async load(request, next) {
        this.state.forms.showLoader();
        let form_load;
        if (!this.state.is_update && !this.state.is_duplicate) {
            let system_form_id = request.query.system_form_id;
            if (system_form_id === undefined) {
                this.router.redirect('forms.items.manager');
                return;
            }
            form_load = this.loadSystemFormItem(system_form_id);
        } else {
            form_load = this.loadCompanyFormItem(request.params.form_id);
        }
        await super.load(request, next);
        form_load.then(async () => {
            if (request.data.form_item_override_category) {
                let {override_id, category_id} = request.data.form_item_override_category;
                this.setFieldValue(this.getField(override_id), category_id);
                delete request.data.form_item_override_category;
            }
            if (request.data.form_item_dependency_product) {
                let {dependency_id, product_id} = request.data.form_item_dependency_product,
                    product = await this.getProductItem(product_id);
                this.setDependencyValue(this.getDependency(dependency_id), {id: product.id, text: product.name}, true);
                delete request.data.form_item_dependency_product;
            }
            this.state.forms.hideLoader();
        }, e => {
            if (e.code === 1007) { // forbidden
                this.router.redirect('forms.items.manager');
                return;
            }
            console.log(e);
        });
    };

    /**
     * Reset to default state
     */
    reset() {
        if (this.state.built) {
            let types = [
                ['templates', 'removeTemplate'],
                ['fields', 'removeField'],
                ['dependencies', 'removeDependency'],
                ['meta', 'removeMeta'],
                ['options', 'clearOption']
            ];
            for (let [type, method] of types) {
                let ids = Object.keys(this.state[type]);
                if (ids.length === 0) {
                    continue;
                }
                for (let id of ids) {
                    this[method](this.state[type][id]);
                }
                this.state[type] = {};
            }
            this.state.field.categories.destroy();
            this.state.form_categories = null;
            this.state.product_categories = null;
            this.state.form.reset();
            this.state.form.destroy();
            this.state.form = null;
            if (this.state.raw_form.structure.type === FormTypes.BID) {
                this.elem.hide_name_wrapper.hide();
                this.elem.bid_settings.hide();
            }
            this.state.current_info = null;
            this.state.current_categories = null;
            this.state.current_relations = null;
            this.state.raw_form = null;
            this.elem.input = {};
            this.state.field = {};
            this.state.form_validated = false;
        }
        if (this.state.preview_modal !== undefined) {
            this.state.preview_modal.unload();
        }
        this.state.built = false;
    };

    /**
     * Unload page
     *
     * @param {object} request
     * @param {function} next
     * @returns {Promise<void>}
     */
    async unload(request, next) {
        this.reset();
        await super.unload(request, next);
    };

    /**
     * Get info payload which contains basic form info and options
     *
     * @returns {object}
     */
    getInfoPayload() {
        let data = Object.assign(cloneDeep(this.state.current_info), {
            name: this.elem.input.name.val() || null
        });
        let option_ids = Object.keys(this.state.options);
        if (option_ids.length > 0) {
            for (let option_id of option_ids) {
                let option = this.state.options[option_id],
                    value = option.elem.is(':checked');
                if (option.data.invert) {
                    value = !value;
                }
                data.item[option.data.key] = value;
            }
        }
        return data;
    };

    /**
     * Get category payload
     *
     * @returns {string[]}
     */
    getCategoryPayload() {
        return this.elem.input.categories.val().sort();
    };

    /**
     * Get payloads for all relations using UI inputs
     *
     * @returns {object}
     */
    getRelationPayload() {
        let relations = [
                ['templates', 'getTemplatePayload'],
                ['fields', 'getFieldPayload'],
                ['dependencies', 'getDependencyPayload'],
                ['meta', 'getMetaPayload']
            ],
            data = {};
        for (let [relation, method] of relations) {
            let ids = Object.keys(this.state[relation]);
            data[relation] = [];
            if (ids.length === 0) {
                continue;
            }
            for (let id of ids) {
                data[relation].push(this[method](this.state[relation][id]));
            }
        }
        return data;
    };

    /**
     * Get full combined payload of info, category, and relation parts
     *
     * @returns {object}
     */
    getFullPayload() {
        return Object.assign(this.getInfoPayload(), {categories: this.getCategoryPayload()}, this.getRelationPayload());
    };

    /**
     * Store snapshot of form payload to router data
     */
    storeSnapshot() {
        this.router.current_route.request.data.form_item_snapshot = this.getFullPayload();
    };

    /**
     * Restore snapshot if available
     *
     * @returns {Promise<boolean>}
     */
    async restoreSnapshot() {
        let payload = this.router.current_route.request.data.form_item_snapshot;
        if (payload !== undefined) {
            await this.populate(payload);
            delete this.router.current_route.request.data.form_item_snapshot;
            return true;
        }
        return false;
    };

    /**
     * Handle save success state
     *
     * @param {boolean} [redirect=false] - Redirect instead of navigate to hide URL from user
     */
    saveSuccess(redirect = false) {
        this.state.forms.hideLoader();
        let message = createSuccessMessage(`Form ${this.state.is_update ? 'edited' : (this.state.is_duplicate ? 'duplicated' : 'added')} successfully`);
        this.router.main_route.layout.toasts.addMessage(message);
        this.router[redirect ? 'redirect' : 'navigate']('forms.items.manager');
    };

    /**
     * Save form
     */
    save() {
        this.state.forms.showLoader();
        let info = this.getInfoPayload(),
            info_changed = !isEqual(this.state.current_info, info),
            categories = this.getCategoryPayload(),
            categories_changed = !isEqual(this.state.current_categories, categories),
            relations = this.getRelationPayload(),
            relations_changed = !isEqual(this.state.current_relations, relations),
            request = null;
        if (!this.state.is_update || info_changed || relations_changed) {
            let full_payload = Object.assign(info, {categories}, relations),
                resource = Api.Resources.CompanyFormItems();
            request = this.state.is_update ? resource.update(full_payload.id, full_payload) : resource.store(full_payload);
            request.then(({data}) => {
                // redirect if id changed, since the old form is not longer available
                this.saveSuccess(data.id !== full_payload.id);
            });
        } else if (categories_changed) {
            request = Api.Resources.CompanyFormItems()
                .method(Api.Request.Method.PUT)
                .custom(`${info.id}/categories`, {categories}).then(() => this.saveSuccess());
        }
        if (request !== null) {
            request.catch(e => {
                let error = 'Unable to save form, please contact support';
                if (e.code === 1007) { // forbidden
                    error = 'Unable to modify archived form';
                }
                this.router.main_route.layout.toasts.addMessage(createErrorMessage(error));
                this.state.forms.hideLoader();
            });
        } else {
            this.saveSuccess();
        }
    };

    /**
     * Get current name of form
     *
     * If user input is empty, then fall back to original.
     *
     * @returns {string}
     */
    getCurrentName() {
        let name = this.elem.input.name.val();
        if (typeof name !== 'string' || name.length === 0) {
            name = this.state.raw_form.name;
        }
        return name;
    };

    /**
     * Get navigation data for use with router redirects
     *
     * @param {object} request
     * @returns {{route: string, query: {}, params: {}}}
     */
    getNavigateData(request) {
        return {
            route: `forms.items.${this.state.is_update ? 'update' : (this.state.is_duplicate ? 'duplicate' : 'create')}`,
            params: request.params,
            query: request.query
        };
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);

        Tooltip.initAll(root);

        this.elem.form = this.elem.root.fxFind('form');
        this.elem.hide_name_wrapper = this.elem.form.fxFind('option-hide-name-wrapper').hide();
        this.elem.overrides = this.elem.form.fxFind('overrides');
        this.elem.dependencies = this.elem.form.fxFind('dependencies');
        this.elem.meta = this.elem.form.fxFind('meta');
        this.elem.bid_settings = this.elem.form.fxFind('bid-settings').hide();

        this.elem.preview = this.elem.root.fxFind('preview');
        this.elem.save = this.elem.root.fxFind('save');

        const that = this;
        this.elem.overrides.fxClickWatcher('create-category', function () {
            let id = $(this).fxClosest('override').data('id'),
                field = that.getField(id);
            that.storeSnapshot();
            let request = that.router.current_route.request,
                instruction = field.config.product_category_create_instruction;
            if (instruction !== undefined) {
                instruction = instruction.replace(/{{name}}/g, that.getCurrentName());
            }
            request.data.form_item_override = {
                navigate: that.getNavigateData(request),
                default_name: field.config.default_product_category_name,
                instruction,
                override_id: id
            };
            that.router.navigate('products.categories.create');
        }, true);
        this.elem.overrides.fxClickWatcher('add-product', function() {
            let id = $(this).fxClosest('override').data('id');
            that.storeSnapshot();
            let request = that.router.current_route.request;
            request.data.form_item_override = {
                navigate: that.getNavigateData(request)
            };
            let category_id = that.state.fields[id].elem.input.val();
            that.router.navigate('products.items.create', {}, {category_id});
        }, true);
        this.elem.dependencies.fxClickWatcher('create-product', function () {
            let id = $(this).fxClosest('dependency').data('id'),
                dependency = that.getDependency(id);
            that.storeSnapshot();
            let request = that.router.current_route.request,
                instruction = dependency.config.product_item_create_instruction;
            if (instruction !== undefined) {
                instruction = instruction.replace(/{{name}}/g, that.getCurrentName());
            }
            request.data.form_item_dependency = {
                navigate: that.getNavigateData(request),
                default_name: dependency.config.default_product_item_name,
                instruction,
                dependency_id: id
            };
            that.router.navigate('products.items.create');
        }, true);

        this.elem.preview.fxClick(() => {
            this.state.preview_mode = true;
            this.elem.form.submit();
        }, true);
        this.elem.save.fxClick(() => {
            this.state.preview_mode = false;
            this.elem.form.submit();
        }, true);
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return create_update_tpl({
            ns: this.state.namespace,
            cancel_route: 'forms.items.manager',
            title: `${this.state.is_update ? 'Edit' : (this.state.is_duplicate ? 'Duplicate' : 'Add')} Form`
        });
    };
}

module.exports = CreateUpdate;
