/**
 * @module BidCreator/Sidebar/Components/SectionManager
 */

'use strict';

const FormDelete = require('../../modals/section/form/delete');

const Accordion = require('../accordion');
const Component = require('../component');
const ComponentNavItem = require('../nav-items/component_nav_item');
const List = require('../list');
const PubSub = require('../../pubsub');

const section_manager_tpl = require('@cam-bid-creator-tpl/sidebar/components/section-manager/main.hbs');
const title_tpl = require('@cam-bid-creator-tpl/sidebar/components/section-manager/title.hbs');

/**
 * @typedef {Object} SectionConfig
 * @property {module:BidCreator/Section} section - Section instance
 * @property {Accordion.Item} item - Accordion item instance
 * @property {List} form_list - List instance for forms
 * @property {Map<string, module:BidCreator/Section/Form>} forms - Associated forms
 */

/**
 * @alias module:BidCreator/Sidebar/Components/SectionManager
 */
class SectionManager extends Component {
    /**
     * Constructor
     *
     * @param {module:BidCreator.Sidebar} sidebar
     * @param {Object} data
     */
    constructor(sidebar, data) {
        super(sidebar, 'section_manager', 'Section Manager');
        this.state.action = {
            label: 'Add Section',
            icon: 'remix-icon--system--add-circle-line',
            handler: () => {
                let section_number = this.state.sections.size + 1;
                this.sidebar.bid.modals.section_add.open(section_number);
            }
        };

        let accordion = new Accordion.Controller({
            sortable: true,
            no_items_text: 'No sections defined, use the button above to add one'
        });
        accordion.on(
            'item-booted',
            /**
             * @listens module:BidCreator/Sidebar/Accordion/Controller~event:itemBooted
             * @param {module:BidCreator/Sidebar/Accordion/Controller~event:itemBooted} event
             */
            (event) => {
                // boot each list after accordion item is booted
                event.item.storage('config').form_list.boot(event.item.elem.panel);
            }
        );

        this.state.accordion = accordion;
        this.state.sections = new Map;
        this.state.handle_reorder = {
            internal_section: true,
            external_section: true,
            internal_form: true,
            external_form: true
        };

        accordion.on('items-reordered', (event) => {
            this.handleInternalReorder(event.order);
        });

        this.sidebar.bid.on('sections-reordered', (event) => {
            this.handleExternalReorder(event.order);
        });

        PubSub.Handler.subscribe(PubSub.Topics.Section.ADD, (message, section) => {
            this.addSection(section);
        });
    };

    /**
     * Get nav item for sidebar
     *
     * @returns {ComponentNavItem}
     */
    getNavItem() {
        return new ComponentNavItem(this.name, {
            icon: 'remix-icon--editor--node-tree',
            classes: ['t-section-manager']
        });
    };

    /**
     * Handle reorder by disabling listener for specified type to prevent infinite loops
     *
     * @param {string} type
     * @param {function} closure
     */
    doReorder(type, closure) {
        this.state.handle_reorder[type] = false;
        closure();
        this.state.handle_reorder[type] = true;
    };

    /**
     * Get section by id
     *
     * @param {string} section_id
     * @returns {(undefined|SectionConfig)}
     */
    getSection(section_id) {
        return this.state.sections.get(section_id);
    };

    /**
     * Add section
     *
     * @param {module:BidCreator/Section} section
     */
    addSection(section) {
        let list = new List({
            type: List.Type.DELETE,
            sortable: true,
            no_items_text: 'No forms',
            action_handler: (item) => {
                this.deleteForm(item.storage.form);
            }
        });
        list.addAction({
            type: List.ActionType.EDIT,
            label: 'Edit Section',
            handler: () => {
                this.sidebar.bid.modals.section_edit.open(section);
            }
        });

        list.on('items-reordered', (event) => {
            this.handleInternalFormReorder(section.id, event.order);
        });

        // handle section delete
        section.on('destroyed', () => {
            this.deleteSection(section.id);
        });

        section.on('form-added', (event) => {
            this.addSectionForm(event.form);
        });

        section.on('forms-reordered', (event) => {
            this.handleExternalFormReorder(section.id, event.order);
        });

        section.on('form-deleted', (event) => {
            this.deleteSectionForm(section.id, event.form.id);
        });

        let $section = {
            section: section,
            forms: new Map,
            form_list: list
        };

        // load any existing forms
        for (let form of section.forms.values()) {
            this.loadSectionForm($section, form);
        }

        let name = title_tpl({
            title: section.name === null ? 'Unsaved Section' : section.name,
            unsaved: section.name === null
        });
        // create accordion item
        let item = new Accordion.Item(name, list.render(), {
            list: list
        });
        item.store('config', $section);

        $section.item = item;

        // handle section rename for item
        section.on('renamed', (event) => {
            item.setTitle(event.new_name);
        });

        this.state.sections.set(section.id, $section);

        this.state.accordion.addItem(item);
    };

    /**
     * Delete form item
     *
     * @param {Object} form - Uuid
     */
    deleteForm(form) {
        new FormDelete().open(form);
    };

    /**
     * Handle reordering from internal accordion
     *
     * Converts accordion item ids into section id array and sends to bid for reordering
     *
     * @param {number[]} items - List of item id's
     */
    handleInternalReorder(items) {
        if (!this.state.handle_reorder.internal_section) {
            return;
        }
        let list = [];
        for (let item_id of items) {
            let item = this.state.accordion.getItem(item_id);
            list.push(item.storage('config').section.id);
        }
        this.doReorder('external_section', () => {
            this.sidebar.bid.updateSectionOrder(list);
        });
    };

    /**
     * Handle reordering from bid
     *
     * Converts section id's into accordion item id array and send to accordion for reordering
     *
     * @param {string[]} items - Array of section uuid's
     */
    handleExternalReorder(items) {
        if (!this.state.handle_reorder.external_section) {
            return;
        }
        let list = [];
        for (let section_id of items) {
            let config = this.getSection(section_id);
            list.push(config.item.id);
        }
        // we have to disable listening for an internal reorder event otherwise we get a nice infinite loop
        this.doReorder('internal_section', () => {
            this.state.accordion.setItemOrder(list);
        });
    };

    /**
     * Delete section
     *
     * @param {string} section_id
     */
    deleteSection(section_id) {
        let section = this.getSection(section_id);
        section.item.delete();
        this.state.sections.delete(section_id);
    };

    /**
     * Get section form by id
     *
     * @param {string} section_id
     * @param {string} form_id
     * @returns {*}
     */
    getSectionForm(section_id, form_id) {
        let section = this.getSection(section_id);
        if (section === undefined) {
            return undefined;
        }
        return section.forms.get(form_id);
    };

    /**
     * Load section form data into forms map of section
     *
     * @param {Object} section
     * @param {Object} form
     * @todo use typedef's here
     */
    loadSectionForm(section, form) {
        section.forms.set(form.id, {
            form: form,
            item_id: section.form_list.addItem(form.name, {
                form_id: form.id,
                form: form
            })
        });
    };

    /**
     * Add section form
     *
     * @param {module:BidCreator/Section/Form} form
     */
    addSectionForm(form) {
        let section = this.getSection(form.section.id);
        this.loadSectionForm(section, form);
    };

    /**
     * Handle form reordering from internal section form list
     *
     * Converts section form list item ids into form id array and sends to related section for reordering
     *
     * @param {string} section_id - Section uuid
     * @param {number[]} items - List of item id's
     */
    handleInternalFormReorder(section_id, items) {
        if (!this.state.handle_reorder.internal_form) {
            return;
        }
        let section = this.getSection(section_id),
            list = [];
        for (let item_id of items) {
            let item = section.form_list.getItem(item_id);
            list.push(item.storage.form.id);
        }
        this.doReorder('external_form', () => {
            section.section.updateFormOrder(list);
        });
    };

    /**
     * Handle form reordering from section
     *
     * Converts form id's into list item id array and send to list for reordering
     *
     * @param {string} section_id - Section uuid
     * @param {string[]} items - Array of form uuid's
     */
    handleExternalFormReorder(section_id, items) {
        if (!this.state.handle_reorder.external_form) {
            return;
        }
        let section = this.getSection(section_id),
            list = [];
        for (let form_id of items) {
            let config = section.forms.get(form_id);
            list.push(config.item_id);
        }
        this.doReorder('internal_form', () => {
            section.form_list.setItemOrder(list);
        });
    };

    /**
     * Delete section form
     *
     * @param {string} section_id - Section uuid
     * @param {string} form_id - Form uuid
     */
    deleteSectionForm(section_id, form_id) {
        let section = this.getSection(section_id),
            form = section.forms.get(form_id);
        section.form_list.deleteItem(form.item_id);
        section.forms.delete(form_id);
    };

    /**
     * Boot section manager component
     *
     * @param {Object} root - jQuery element for panel
     */
    boot(root) {
        super.boot(root);

        this.elem.sections = this.elem.content.fxFind('sections');

        this.state.accordion.boot(this.elem.sections);
    };

    /**
     * Render panel content
     *
     * @returns {string}
     */
    renderPanelContent() {
        return section_manager_tpl({
            accordion: this.state.accordion.render()
        });
    };
}

module.exports = SectionManager;
