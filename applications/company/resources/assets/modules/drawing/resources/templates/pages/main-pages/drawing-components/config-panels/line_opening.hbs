<div class="m-line-opening-config-panel">
{{#if width_enabled}}
    <div class="c-locp-option">
        <div class="f-field">
            <label class="c-locpo--label f-f-label">Width:</label>
            <div class="c-locpo--measurements" data-js="measurements">
                <input type="text" class="f-f-input" data-fx-form-input="number-spinner" data-js="width">
            </div>
            <div class="c-locpo--error" data-js="width-error"></div>
        </div>
    </div>
{{/if}}
{{#if swing_enabled}}
    <div class="c-locp-option">
        <div class="f-field">
            <label class="c-locpo--label f-f-label">Swing:</label>
            <select class="f-f-input" data-fx-form-input="button-group" data-js="swing">
                {{#each swings}}
                    <option value="{{@key}}">{{this}}</option>
                {{/each}}
            </select>
        </div>
    </div>
{{/if}}
{{#if side_enabled}}
    <div class="c-locp-option">
        <div class="f-field">
            <label class="c-locpo--label f-f-label">Side:</label>
            <select class="f-f-input" data-fx-form-input="button-group" data-js="side">
                {{#each sides}}
                    <option value="{{@key}}">{{this}}</option>
                {{/each}}
            </select>
        </div>
    </div>
{{/if}}
</div>
