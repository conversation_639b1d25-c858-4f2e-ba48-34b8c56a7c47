'use strict';

const Styled = require('../styled');
const Tool = require('../../../tools/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Nodes/Rectangle/Styled
 */
class Concrete extends Styled {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Paper} paper
     * @param {(object|module:Drawing/Entities/Drawing.Node)} data
     */
    constructor(paper, data = {}) {
        super(paper, data);
        Object.assign(this.properties, {
            type: Concrete.Entity.Type.CONCRETE,
            tool_type: Tool.Type.CONCRETE,
            handles: Object.assign(this.properties.handles, {
                scale: true,
                resize: true,
                rotate: true
            }),
            show_dimensions: true,
            // label: 'Concrete'
        });
    };

    /**
     * Get default state for node type
     *
     * @param {object} state
     * @returns {object}
     */
    getDefaultState(state) {
        state = super.getDefaultState(state);
        Object.assign(state, {
            color: 'rgba(130,130,130,0.5)'
        });
        return state;
    };
}

module.exports = Concrete;
