/**
 * @module Drawing/Pages/MainPages/ManagerPages
 */

'use strict';

const FlashMessage = require('@ca-submodule/flash-message');
const Table = require('@ca-submodule/table').Base;

const Page = require('../../page');
const DrawingRepo = require('../../repositories/drawing');
const DrawingEntity = require('../../entities/drawing');
const PubSub = require('../../pubsub');

const network = require('../../network');
const log = require('../../log');

const manager_tpl = require('@cam-drawing-tpl/pages/main-pages/manager.hbs');

/**
 * @memberof module:Drawing/Pages/MainPages
 */
class Manager extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);
        Object.assign(this.state, {
            drawings_loaded: false,
            table: null,
            server_search: false,
            flash_message: new FlashMessage.Controller(),
            offline_message: null,
            table_scope: {
                sorts: {
                    created_at: Table.Sort.DESC
                }
            }
        });
    };

    /**
     * Get child routes
     *
     * @returns {object}
     */
    static get routes() {
        return {
            create: {
                path: '/create',
                modal: require('./manager-pages/create')
            },
            update: {
                path: '/update/{drawing_id}',
                modal: require('./manager-pages/update'),
                bindings: {
                    drawing_id: 'uuid'
                }
            },
            duplicate: {
                path: '/duplicate/{drawing_id}',
                modal: require('./manager-pages/duplicate'),
                bindings: {
                    drawing_id: 'uuid'
                }
            },
            delete: {
                path: '/delete/{drawing_id}',
                modal: require('./manager-pages/delete'),
                bindings: {
                    drawing_id: 'uuid'
                }
            }
        };
    };

    /**
     * Get flash message controller
     *
     * @returns {module:FlashMessage.Controller}
     */
    get flash_message() {
        return this.state.flash_message;
    };

    /**
     * Configure and render table module
     */
    renderTable() {
        let status_map = new Map([
            [null, '<span class="h-text t-yellow">In Progress</span>'],
            [DrawingEntity.Status.IN_PROGRESS, '<span class="h-text t-yellow">In Progress</span>'],
            [DrawingEntity.Status.FINALIZED, '<span class="h-text t-green">Finalized</span>']
        ]);
        let sync_status_map = new Map([
            [DrawingEntity.SyncStatus.PENDING, '<span class="h-text t-blue">Pending</span>'],
            [DrawingEntity.SyncStatus.SYNCING, '<span class="h-text t-blue">Syncing</span>'],
            [DrawingEntity.SyncStatus.SYNCED, '<span class="h-text t-green">Synced</span>'],
            [DrawingEntity.SyncStatus.FAILED, '<span class="h-text t-red">Failed</span>']
        ]);
        let table = new Table(this.elem.table_container, {
            searching: (config) => {
                if (network.online) {
                    config.fetch_data = true;
                    this.state.server_search = true;
                } else {
                    // if previous search was server based, then we fetch again from local database
                    if (this.state.server_search) {
                        config.fetch_data = true;
                        this.state.server_search = false;
                    }
                    config.row_search = true;
                }
                return config;
            }
        });
        table.on('row_click', (data) => {
            if (data.status !== DrawingEntity.Status.IN_PROGRESS) {
                return;
            }
            this.router.navigate('drawing', {
                drawing_id: data.id
            })
        })
            .on('search_clear', () => {
                // clear search term no matter what in case they switch from online to offline searching in the same
                // search session (rare, but came up in testing)
                table.setSearch('', !this.state.server_search);
                if (this.state.server_search) {
                    table.fetchData();
                    this.state.server_search = false;
                }
            });
        table.setHeader({
            custom_search: true,
            search: true,
            search_placeholder: 'Search drawings'
        });
        table.setToolbar({
            filter: false,
            settings: false
        });
        table.setColumns({
            name: {
                label: 'Name'
            },
            status: {
                label: 'Status',
                width: '5%',
                value: (data, type) => type === 'display' ? status_map.get(data.status) : data.status
            },
            customer: {
                label: 'Customer',
                value: (data, type) => data.project === null ? (type === 'display' ? '<em>--</em>' : '') : data.project.customer_name
            },
            property: {
                label: 'Property',
                value: (data, type) => data.project === null ? (type === 'display' ? '<em>--</em>' : '') : data.project.property_address
            },
            project: {
                label: 'Project',
                value: (data, type) => data.project === null ? (type === 'display' ? '<em>Unassigned</em>' : 'Unassigned') : data.project.description
            },
            created_at: {
                label: 'Created At',
                value: (data, type) => {
                    let date = data.getStartedAt();
                    if (type === 'display' || type === 'filter') {
                        return date.tz(this.parent.layout.user.timezone).format('MM/DD/YYYY h:mma');
                    }
                    return date.toISOString();
                }
            },
            created_by: {
                label: 'Created By',
                key: 'created_by_user.full_name'
            },
            sync_status: {
                label: 'Sync Status',
                width: '5%',
                value: (data, type) => {
                    if (this.state.server_search) {
                        return type === 'display' ? '<em>--</em>' : '';
                    }
                    return type === 'display' ? sync_status_map.get(data.sync_status) : data.sync_status;
                }
            }
        });
        table.setRowActions({
            retry_sync: {
                label: 'Retry Sync',
                visible: data => data.sync_status === DrawingEntity.SyncStatus.FAILED,
                action: async data => {
                    await DrawingRepo.partialUpdate(data.id, {
                        sync_status: DrawingEntity.SyncStatus.PENDING
                    });
                    await this.controller.sync_handler.pushDrawings();
                }
            },
            open: {
                label: 'Open',
                visible: data => data.status !== DrawingEntity.Status.FINALIZED,
                action: data => this.router.navigate('drawing', {drawing_id: data.id})
            },
            edit_details: {
                label: 'Edit Details',
                visible: data => data.status !== DrawingEntity.Status.FINALIZED,
                action: data => this.router.navigate('manager.update', {drawing_id: data.id})
            },
            duplicate: {
                label: 'Duplicate',
                action: data => this.router.navigate('manager.duplicate', {drawing_id: data.id})
            },
            view_project: {
                label: 'View Project',
                visible: data => data.project_id !== null,
                disabled: () => !network.online,
                link: {
                    href: data => window.drawing_data.project_management_url.replace('{project_id}', data.project_id)
                }
            },
            download_image: {
                label: 'Download Svg',
                visible: data => data.status === DrawingEntity.Status.FINALIZED && data.sync_status === DrawingEntity.SyncStatus.SYNCED,
                disabled: () => !network.online,
                link: {
                    href: data => `${window.fx_url.BASE}media/drawing-images/original/${data.id}?download=true`,
                    target: '_blank'
                }
            },
            view_pdf: {
                label: 'View PDF',
                visible: data => data.status === DrawingEntity.Status.FINALIZED && data.sync_status === DrawingEntity.SyncStatus.SYNCED,
                disabled: () => !network.online,
                link: {
                    href: data => `${window.fx_url.BASE}media/repair-plans/original/${data.id}`,
                    target: '_blank'
                }
            },
            delete: {
                label: 'Delete',
                negate: true,
                action: data => this.router.navigate('manager.delete', {drawing_id: data.id})
            }
        });
        table.setButtons({
            sync: {
                label: 'Sync',
                action: () => {
                    let sync_handler = this.controller.sync_handler;
                    if (sync_handler.isInProgress()) {
                        return;
                    }
                    sync_handler.run().catch((error) => log.error('Unable to run sync from button', {error}));
                }
            },
            create: {
                label: 'Create Drawing',
                action: () => this.router.navigate('manager.create'),
                type_class: 't-primary'
            }
        });
        table.setData(async (scope) => {
            if (network.online && scope.search !== null) {
                return await DrawingRepo.search(scope.search);
            }
            return await DrawingRepo.all(['created_by_user']);
        });
        // modify table state
        if (this.state.table_scope) {
            table.setState(this.state.table_scope);
        }
        table.build();

        this.state.table = table;

        PubSub.Handler.subscribe(PubSub.Topics.Drawing.STORE, async (message, data) => {
            await this.tableRowPut(data.entity);
        });
        PubSub.Handler.subscribe(PubSub.Topics.Drawing.UPDATE, async (message, data) => {
            await this.tableRowUpdate(data.id);
        });
        PubSub.Handler.subscribe(PubSub.Topics.Drawing.DELETE, (message, data) => {
            this.tableRowDelete(data.id);
        });
    };

    /**
     * Create or update table row
     *
     * @param {module:Drawing/Entities.Drawing} entity
     * @returns {Promise<void>}
     */
    async tableRowPut(entity) {
        if (this.state.server_search) {
            return;
        }
        let row = this.state.table.getRowById(entity.id);
        await entity.load(['created_by_user']);
        if (row === null) {
            this.state.table.addRow(entity);
        } else {
            this.state.table.updateRow(entity, row);
        }
    };

    /**
     * Update existing table row
     *
     * @param {string} id
     * @returns {Promise<void>}
     */
    async tableRowUpdate(id) {
        if (this.state.server_search) {
            return;
        }
        let entity = await DrawingRepo.findById(id, ['created_by_user']);
        this.state.table.updateRow(entity);
    };

    /**
     * Delete table row by id
     *
     * @param {string} id
     */
    tableRowDelete(id) {
        if (this.state.server_search) {
            return;
        }
        this.state.table.deleteRow(id);
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        this.parent.startWorking();
        await super.load(request, next);
        // if this is the first page loaded, we run a full sync. we skip checking the user since it just
        // happened by the main page
        if (this.router.current_route.is_initial) {
            try {
                await this.controller.sync_handler.run(false);
            } catch (error) {
                log.error('Unable to run sync', {error});
            }
        } else {
            // otherwise we just pull drawings, pushing is initiated by drawing panel and modals when needed
            this.controller.sync_handler.pullDrawings().catch(error => log.error('Unable to pull drawings', {error}));
        }
        if (!this.state.drawings_loaded) {
            this.renderTable();
            this.state.drawings_loaded = true;
        }
        this.parent.resetWorking();
    };

    /**
     * Show offline flash message
     */
    showOffline() {
        if (this.state.offline_message !== null) {
            return;
        }
        this.state.offline_message = new FlashMessage.Message('You appear to be offline', FlashMessage.Message.Type.ERROR);
        this.flash_message.addMessage(this.state.offline_message);
    };

    /**
     * Hide offline flash message
     */
    hideOffline() {
        if (this.state.offline_message === null) {
            return;
        }
        this.state.offline_message.delete();
        this.state.offline_message = null;
    };

    /**
     * Show or hide offline message depending on online boolean
     *
     * @param {boolean} online
     */
    handleNetworkStatus(online) {
        this[online ? 'hideOffline' : 'showOffline']();
    };

    /**
     * Boot manager panel
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        this.elem.table_container = this.elem.root.fxFind('table-container');
        this.elem.flash_messages = this.elem.root.fxFind('flash-messages');

        this.state.flash_message.boot(this.elem.flash_messages);

        this.handleNetworkStatus(network.online);
        network.on('status-changed', ({online}) => {
            this.handleNetworkStatus(online);
        });
    };

    /**
     * Render manager panel
     */
    render() {
        return manager_tpl({
            flash_messages: this.state.flash_message.render()
        });
    };
}

module.exports = Manager;
