'use strict';

const Paper = require('../../paper');
const Stamp = require('../stamp');
const Tool = require('../../tools/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Nodes/Stamp
 */
class Vegetation extends Stamp {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Paper} paper
     * @param {(object|module:Drawing/Entities/Drawing.Node)} data
     */
    constructor(paper, data = {}) {
        super(paper, data);
        Object.assign(this.properties, {
            type: Vegetation.Entity.Type.VEGETATION,
            tool_type: Tool.Type.VEGETATION,
            layer: Paper.Layer.BACKGROUND_STAMP,
            handles: Object.assign(this.properties.handles, {
                scale: true
            }),
            sizes: {
                template: new paper.ps.Size([50, 50]),
                min: paper.getSizeFromUnits({feet: 1})
            }
        });
    };

    /**
     * Get default state for node
     *
     * @param {object} state
     * @returns {object}
     */
    getDefaultState(state) {
        state = super.getDefaultState(state);
        Object.assign(state, {
            size: this.paper.getSizeFromUnits({feet: 3})
        });
        return state;
    };

    /**
     * Create paper.js path template to clone from
     *
     * @returns {*}
     */
    getPathTemplate() {
        let path = new this.paper.ps.Path({
            fillColor: [0.52157, 0.9098, 0.67059],
            strokeColor: '#000',
            insert: false,
            segments: [
                [8.255, 42.065], [4.85, 38.36], [4.85, 36.446], [3.533, 35.998], [1.769, 32.17], [1, 26.976],
                [2.49, 25.062], [1.174, 22.129], [1.894, 19.469], [2.938, 15.169], [5.149, 11.341], [7.509, 11.341],
                [9.273, 11.788], [8.403, 9.402], [8.851, 7.339], [12.23, 4.256], [15.783, 2.765], [17.993, 3.511],
                [19.311, 4.256], [19.311, 2.317], [21.248, 1.149], [27.584, 1], [31.857, 1.72], [32.901, 3.063],
                [33.199, 4.381], [35.112, 3.958], [37.199, 4.381], [40.876, 7.19], [43.386, 9.551], [45.298, 12.211],
                [45.149, 14.15], [44.429, 15.02], [46.491, 15.169], [47.236, 16.362], [49, 22.253], [48.85, 29.064],
                [47.062, 34.533], [45.745, 36.447], [43.832, 36.447], [44.28, 37.789], [44.429, 39.405],
                [39.981, 44.575], [36.727, 46.042], [33.646, 47.658], [31.286, 47.086], [30.839, 46.042],
                [30.242, 47.956], [22.565, 49], [17.547, 48.105], [13.845, 46.639], [9.571, 44.128], [9.87, 42.214]
            ],
            closed: true
        });
        return new this.paper.ps.Group({
            children: [path]
        });
    };
}

module.exports = Vegetation;
