'use strict';

const Stamp = require('../stamp');
const Tool = require('../../tools/base');

/**
 * @memberof module:Drawing/Pages/MainPages/DrawingComponents/Nodes/Stamp
 */
class Utility extends Stamp {
    /**
     * Constructor
     *
     * @param {module:Drawing/Pages/MainPages/DrawingComponents.Paper} paper
     * @param {(object|module:Drawing/Entities/Drawing.Node)} data
     */
    constructor(paper, data = {}) {
        super(paper, data);
        Object.assign(this.properties, {
            type: Utility.Entity.Type.UTILITY,
            tool_type: Tool.Type.UTILITY,
            handles: Object.assign(this.properties.handles, {
                scale: true
            }),
            sizes: {
                template: new paper.ps.Size([50, 50]),
                min: paper.getSizeFromUnits({feet: 1})
            }
        });
    };

    /**
     * Get default state for node
     *
     * @param {object} state
     * @returns {object}
     */
    getDefaultState(state) {
        state = super.getDefaultState(state);
        Object.assign(state, {
            size: this.paper.getSizeFromUnits({feet: 2})
        });
        return state;
    };

    /**
     * Create paper.js path template to clone from
     *
     * @returns {*}
     */
    getPathTemplate() {
        let rect = new this.paper.ps.Path.Rectangle({
                size: this.properties.sizes.template,
                radius: 6,
                strokeWidth: 1,
                strokeColor: '#000',
                fillColor: '#fff',
                insert: false
            });
        // 'U' shape as path so it will scale properly
        let letter = new this.paper.ps.Path({
            fillColor: [0, 0, 0],
            insert: false,
            segments: [
                [28.98092, 17], [31, 17], [[31, 26.0892], [0, 0], [0, 1.58105]],
                [[30.47962, 29.8558], [0.34692, -0.93003], [-0.34692, 0.93003]],
                [[28.60104, 32.12542], [0.90547, -0.58306], [-0.90547, 0.58306]],
                [[25.03643, 33], [1.47095, 0], [-1.42932, 0]],
                [[21.52905, 32.2381], [0.90894, 0.50794], [-0.90894, -0.50794]],
                [[19.58283, 30.03286], [0.38855, 0.96222], [-0.38855, -0.96222]], [[19, 26.0892], [0, 1.6669], [0, 0]],
                [19, 17], [21.01908, 17], [[21.01908, 26.07847], [0, 0], [0, 1.36643]],
                [[21.38855, 29.09926], [-0.24632, -0.64744], [0.24632, 0.64744]],
                [[22.65828, 30.59624], [-0.60018, -0.35055], [0.60018, 0.35055]],
                [[24.8595, 31.12207], [-0.86731, 0], [1.48483, 0]],
                [[28.03382, 30.08115], [-0.6314, 0.69394], [0.6314, -0.69394]],
                [[28.98092, 26.07847], [0, 1.97452], [0, 0]]
            ],
            closed: true
        });
        return new this.paper.ps.Group({
            children: [rect, letter]
        });
    };
}

module.exports = Utility;
