'use strict';

const includes = require('lodash/includes');
const moment = require('moment-timezone');

const getIso8601DateTime = require('@cac-js/utils/iso8601_datetime');

const Base = require('./base');

const SyncStatus = {
    PENDING: 1,
    SYNCED: 2,
    FAILED: 3,
    SYNCING: 4
};

/**
 * Base class for all syncable entities
 *
 * @memberof module:Drawing/Entities
 * @abstract
 */
class SyncableEntity extends Base {
    /**
     * Get available sync statuses
     *
     * @readonly
     *
     * @returns {object}
     */
    static get SyncStatus() {
        return SyncStatus;
    };

    /**
     * Set initial state of entity from input data
     *
     * @param {object} data
     * @param {boolean} existing
     */
    setInitialState(data, existing) {
        super.setInitialState(data, existing);
        Object.assign(this.state, {
            is_deleted: false,
            is_published: false,
            sync_status: SyncStatus.PENDING,
            sync_started_at: null
        });
        this.fill(['is_deleted', 'is_published', 'sync_status', 'sync_started_at'], data);
    };

    /**
     * Clone internal state
     *
     * @param {object} state
     * @param {boolean} duplicate
     * @returns {object}
     */
    cloneState(state, duplicate) {
        state = super.cloneState(state, duplicate);
        if (duplicate) {
            state.is_deleted = false;
            state.is_published = false;
            state.sync_status = SyncStatus.PENDING;
            state.sync_started_at = null;
        }
        return state;
    };

    /**
     * Get is deleted status
     *
     * @returns {boolean}
     */
    get is_deleted() {
        return this.state.is_deleted;
    };

    /**
     * Set is deleted
     *
     * @param {(number|boolean)} is_deleted
     */
    set is_deleted(is_deleted) {
        if (typeof is_deleted !== 'boolean') {
            is_deleted = is_deleted === 1;
        }
        this.setState({is_deleted});
    };

    /**
     * Get is published status
     *
     * @returns {boolean}
     */
    get is_published() {
        return this.state.is_published;
    };

    /**
     * Set is published
     *
     * @param {(number|boolean)} is_published
     */
    set is_published(is_published) {
        if (typeof is_published !== 'boolean') {
            is_published = is_published === 1;
        }
        this.setState({is_published});
    };

    /**
     * Get sync status
     *
     * @returns {number}
     */
    get sync_status() {
        return this.state.sync_status;
    };

    /**
     * Set sync status
     *
     * @param {number} sync_status
     */
    set sync_status(sync_status) {
        if (!includes(SyncStatus, sync_status)) {
            throw new Error('Sync status is not valid');
        }
        this.setState({sync_status});
    };

    /**
     * Get sync started at datetime
     *
     * @param {boolean} [as_date=true] - determines if moment instance is returned
     * @returns {(null|moment|string)}
     */
    getSyncStartedAt(as_date = true) {
        if (this.state.sync_started_at === null) {
            return null;
        }
        return as_date ? moment(this.state.sync_started_at) : this.state.sync_started_at;
    };

    /**
     * Get sync started at datetime
     *
     * @returns {(null|string)}
     */
    get sync_started_at() {
        return this.getSyncStartedAt(false);
    };

    /**
     * Set last modified at datetime
     *
     * @param {(null|Date|string)} sync_started_at - must be ISO8601 formatted UTC datetime string
     */
    set sync_started_at(sync_started_at) {
        if (sync_started_at instanceof Date) {
            sync_started_at = getIso8601DateTime(sync_started_at);
        }
        this.setState({sync_started_at});
    };

    /**
     * Get payload for storage
     *
     * @returns {object}
     */
    getPayload() {
        return Object.assign(super.getPayload(), {
            is_deleted: this.is_deleted ? 1 : 0,
            is_published: this.is_published ? 1 : 0,
            sync_status: this.sync_status,
            sync_started_at: this.sync_started_at
        });
    };
}

module.exports = SyncableEntity;
