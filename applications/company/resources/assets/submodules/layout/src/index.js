/**
 * @module Layout
 */

'use strict';

import moment from 'moment-timezone';


import '../resources/svg-symbols/*.svg';
import 'remixicon/icons/Arrows/arrow-left-s-line.svg';
import 'remixicon/icons/Arrows/arrow-left-line.svg';
import 'remixicon/icons/Arrows/arrow-right-s-line.svg';
import 'remixicon/icons/Arrows/arrow-right-line.svg';
import 'remixicon/icons/Arrows/arrow-down-s-line.svg';
import 'remixicon/icons/Arrows/arrow-drop-down-line.svg';
import 'remixicon/icons/System/add-line.svg';
import 'remixicon/icons/User & Faces/user-add-line.svg';
import 'remixicon/icons/User & Faces/user-line.svg';
import 'remixicon/icons/User & Faces/user-shared-fill.svg';
import 'remixicon/icons/Document/folders-line.svg';
import 'remixicon/icons/Business/customer-service-line.svg';
import 'remixicon/icons/Buildings/building-line.svg';
import 'remixicon/icons/Finance/money-dollar-circle-line.svg';
import 'remixicon/icons/System/list-settings-line.svg';
import 'remixicon/icons/System/logout-circle-r-line.svg';
import 'remixicon/icons/User & Faces/account-circle-line.svg';
import 'remixicon/icons/User & Faces/account-pin-circle-line.svg';
import 'remixicon/icons/System/check-double-line.svg';
import 'remixicon/icons/System/check-line.svg';
import 'remixicon/icons/System/search-line.svg';
import 'remixicon/icons/System/settings-3-line.svg';
import 'remixicon/icons/Business/archive-line.svg';
import 'remixicon/icons/Business/calendar-line.svg';
import 'remixicon/icons/Business/calendar-todo-line.svg';
import 'remixicon/icons/Business/calendar-todo-fill.svg';
import 'remixicon/icons/Business/inbox-archive-line.svg';
import 'remixicon/icons/Business/inbox-archive-fill.svg';
import 'remixicon/icons/Business/inbox-line.svg';
import 'remixicon/icons/Document/folder-open-line.svg';
import 'remixicon/icons/Document/clipboard-line.svg';
import 'remixicon/icons/Document/clipboard-fill.svg';
import 'remixicon/icons/Document/file-list-2-line.svg';
import 'remixicon/icons/Document/file-list-2-fill.svg';
import 'remixicon/icons/Document/file-close-line.svg';
import 'remixicon/icons/Document/file-close-fill.svg';
import 'remixicon/icons/Document/file-check-line.svg';
import 'remixicon/icons/Document/file-check-fill.svg';
import 'remixicon/icons/Others/box-3-line.svg';
import 'remixicon/icons/Media/notification-2-line.svg';
import 'remixicon/icons/Media/picture-in-picture-fill.svg';
import 'remixicon/icons/Design/tools-line.svg';
import 'remixicon/icons/Design/tools-fill.svg';
import 'remixicon/icons/System/checkbox-blank-circle-line.svg';
import 'remixicon/icons/System/checkbox-circle-line.svg';
import 'remixicon/icons/System/checkbox-circle-fill.svg';
import 'remixicon/icons/System/checkbox-line.svg';
import 'remixicon/icons/System/checkbox-fill.svg';
import 'remixicon/icons/System/close-line.svg';
import 'remixicon/icons/System/indeterminate-circle-fill.svg';
import 'remixicon/icons/System/information-line.svg';
import 'remixicon/icons/System/information-fill.svg';
import 'remixicon/icons/System/notification-badge-line.svg';
import 'remixicon/icons/System/share-circle-line.svg';
import 'remixicon/icons/System/error-warning-line.svg';
import 'remixicon/icons/System/error-warning-fill.svg';
import 'remixicon/icons/System/more-line.svg';
import 'remixicon/icons/System/delete-bin-2-line.svg';

import '@cac-loader/spinning-circle.svg';

import helphero from 'helphero';

import {find, findChild, findFirstChild, jsSelector, onClick} from '@ca-package/dom';
import event_mixin from '@cac-js/mixins/event';
import {getController as getToastController} from '@cas-notification-toast-js';
import {createSuccessMessage} from '@cas-notification-toast-js/message/success';
import {createInfoMessage} from '@cas-notification-toast-js/message/info';
import {createErrorMessage} from '@cas-notification-toast-js/message/error';

import * as MediaQuery from '@ca-package/media-query';

import {Overlay as GlobalSearch} from './global-search/overlay';
import {Desktop} from './media/desktop';
import {Mobile} from './media/mobile';
import Api from "@ca-package/api";
import NotificationManager from "@cas-layout-js/notifications/manager";
import NotificationCenter from "@cas-layout-js/notifications/components/notification_center";
import NotificationService from "@cas-layout-js/notifications/service";

const $ = require('jquery');

const NotificationHelper  = require('@ca-submodule/layout/src/notifications/helpers');

const Modal = {
    AppNotification: require('@ca-submodule/modal').AppNotification,
};

const Media = {
    desktop: {
        type: Desktop,
        query: ['>xlarge']
    },
    mobile: {
        type: Mobile,
        query: ['<=xlarge']
    }
};
export const Modes = {
    FLUID: 1,
    WINDOW: 2
};
const MessageFactory = {
    alert: (message, delete_after) => createErrorMessage(message, {delete_after}),
    info: (message, delete_after) => createInfoMessage(message, {delete_after}),
    success: (message, delete_after) => createSuccessMessage(message, {delete_after})
};

/**
 * @typedef {object} User
 * @property {string} uuid
 * @property {string} first_name
 * @property {string} last_name
 * @property {string} email
 * @property {string} timezone - Company default or user overridden timezone identifier
 * @property {string} created_at - ISO8601 date in UTC
 * @property {number} days_since_creation - Number of days user has existed
 * @property {string} brand_slug
 * @property {string} brand_name
 * @property {string} company_name
 */

/**
 * @memberof module:Layout
 * @mixes Event
 */
class Controller {
    /**
     * Layout constructor
     */
    constructor() {
        this.elem = {};
        this.state = {
            title: null,
            walkthrough: {
                enabled: false,
                app_id: null,
                user_id_env: null
            },
            user: null,
            loader_visible: false,
            restricted: false,
            mode: Modes.FLUID,
            prev_mode: null,
            global_search: null,
            modal: {},
        };

        let group = new MediaQuery.Group();
        for (let name of Object.keys(Media)) {
            group.add(name, ...Media[name].query);
        }
        group.events.on('changed', ({matches}) => this.handleMedia(matches));
        this.state.media_query_group = group;

        let data = window.layout ?? {};
        this.state.env = data.env ?? 'PROD';
        this.state.restricted = data.restricted ?? false;
        if (data.config) {
            this.setConfig(data.config);
        }
        if (data.user) {
            this.setUser(data.user);
        }
        this.setTitle(data.title ?? null, false, false);
        this.boot();
        this.setMode(Modes.FLUID);

        let message = data.message;
        if (message !== null) {
            let delete_after = message.auto_delete === undefined || message.auto_delete ? 10 : undefined,
                toast = MessageFactory[message.type]?.(message.message, delete_after);
            this.toasts.addMessage(toast);
        }

        let user_info = window.layout.user;
        if (user_info.is_trial) {
            let trial_expires = moment(user_info.trial_expires),
                current_date = moment().format('YYYY-MM-DD'),
                days_remaining = trial_expires.diff(current_date, 'days'),
                trial_class,
                trial_content,
                is_primary = user_info.role_primary,
                demo_link = 'https://cxlratr.to/app-demo',
                support_email = '<EMAIL>';

            switch(true) {
                case (days_remaining === 21):
                    trial_class = 't-blue';
                    trial_content = `<strong>Welcome to your 3 week free trial!</strong>`;
                    if (is_primary) {
                        trial_content = `${trial_content} &nbsp;&nbsp;<a target="_blank" href="${demo_link}">Click here</a> to schedule a demo with our team.`;
                    }
                    break;
                case (days_remaining > 14):
                    trial_class = 't-blue';
                    trial_content = `<strong>You have ${days_remaining} days left!</strong>`;
                    if (is_primary) {
                        trial_content = `${trial_content} &nbsp;&nbsp;<a target="_blank" href="${demo_link}">Click here</a> to schedule a demo with our team.`;
                    }
                    break;
                case (days_remaining === 14):
                    trial_class = 't-blue';
                    trial_content = `<strong>Welcome to your 14-day free trial!</strong>`;
                    if (is_primary) {
                        trial_content = `${trial_content} &nbsp;&nbsp;<a target="_blank" href="${demo_link}">Click here</a> to schedule a demo with our team.`;
                    }
                    break;
                case (days_remaining <= 13 && days_remaining >= 5):
                    trial_class = 't-yellow';
                    if (is_primary) {
                        trial_content = `<strong>${days_remaining} days left!</strong>&nbsp;&nbsp;<a target="_blank" href="mailto:${support_email}">Click here</a> to connect with our support team.`;
                    } else {
                        trial_content = `<strong>Your trial has ${days_remaining} days left!</strong>`;
                    }
                    break;
                case (days_remaining === 4 || days_remaining === 3):
                    trial_class = 't-red';
                    if (is_primary) {
                        trial_content = `<strong>You have only ${days_remaining} days left!</strong>&nbsp;&nbsp;<a target="_blank" href="mailto:${support_email}">Click here</a> to connect with our support team.`;
                    } else {
                        trial_content = `<strong>You have only ${days_remaining} days left in your trial!</strong>`;
                    }
                    break;
                case (days_remaining === 2):
                    trial_class = 't-red';
                    trial_content = `<strong>Your FREE trial ends TOMORROW!</strong>`;
                    if (is_primary) {
                        trial_content = `${trial_content}&nbsp;&nbsp;<a target="_blank" href="mailto:${support_email}">Click here</a> to connect with our support team.`;
                    }
                    break;
                case (days_remaining <= 1):
                    trial_class = 't-red';
                    trial_content = `<strong>Your FREE trial is expiring TODAY!</strong>`;
                    if (is_primary) {
                        trial_content = `${trial_content}&nbsp;&nbsp;<strong>Don't worry, you won't loose access.</strong> We'll charge your monthly subscription to the card on file.&nbsp;&nbsp;<a target="_blank" href="mailto:${support_email}">Click here</a> to contact support with questions.`;
                    }
                    break;
            }
            this.elem.notification.addClass(trial_class);
            this.elem.notification_text.html(trial_content);
        }

      this.bootNotificationsCenter();
    };

    bootNotificationsCenter() {
        new NotificationManager();
        this.notificationCenter = new NotificationCenter(this.elem.root);

        this.bindGlobalEvents();
        this.handleBannerNotification();
        this.notificationCenter.loadNotifications();
    }

    bindGlobalEvents() {
        let resizeTimeout;

        $(window).on('notification:read', (event, payload) => {
            if (!payload || !payload.distribution_id || !payload.placement) return;

            const can_remove_banner =
                payload.placement !== Api.Constants.AppNotification.Placement.NOTIFICATION_CENTER &&
                !payload.is_completion_required;


            if (can_remove_banner) {
                $(window).trigger('notification:banner-remove');
            }

            NotificationService.removeNotification(payload.distribution_id, payload.placement);
            this.notificationCenter.loadNotifications();
        });

        $(window).on('notification:batch-read', () => {
            const all_notifications = window.layout?.user?.notifications?.notification_center || [];

            const ids = all_notifications
                .map(n => n?.distribution.id)
                .filter(Boolean);

            if (!ids.length) return;

            const is_processing_spinner = $('.loading-spinner-container');
            is_processing_spinner.addClass('active');

            NotificationHelper.acknowledgeAll(ids).then(updated_ids => {
                NotificationService.removeMultipleNotifications(updated_ids, Api.Constants.AppNotification.Placement.GLOBAL);
                this.notificationCenter.loadNotifications();
            }).finally(() => {
                this.notificationCenter.state.selected_notifications = [];
                is_processing_spinner.removeClass('active');
            })
        });


        // Visual changes
        $(window).on('notification:sidebar-style-update', this.notificationCenter.updateSidebar);

        $(window).on('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(this.notificationCenter.updateSidebar, 150);
            $(window).trigger('notification:sidebar-close');
        });

        $(window).on('load', this.notificationCenter.updateSidebar);
    }

    handleBannerNotification() {
        this.banner_selector = jsSelector('banner-notification');
        const $banner = findChild(this.elem.root, this.banner_selector);
        const notifications = window.layout.user?.notifications?.banner || []
        const notification_types = {
            1: { 'type': 'info', 'class': 't-blue' },
            2: { 'type': 'maintenance', 'class': 't-yellow' },
            3: { 'type': 'error', 'class': 't-red' }
        }

        if (notifications.length > 0) {
            const notification = notifications[0]
            const type_config = notification_types[notification.type];

            notification.primary_button_action = () => {
                if (notification.link) {
                    window.location.href= window.fx_url.BASE + notification.link;
                }
            }

            notification.secondary_button_action = () => {
                const { distribution, placement } = notification;
                NotificationHelper.acknowledge(distribution.id, Api.Constants.AppNotificationDistribution.Status.READ);
                $(document).trigger('notification:read', {
                    distribution_id: distribution.id,
                    is_completion_required: notification.is_completion_required,
                    placement,
                });
            }

            $banner.addClass(type_config.class);
            $banner.on('click', () => {
                $(document).trigger('notification:clicked', [notification]);
            });
        }
    }



    /**
     * Get message modal
     *
     * If modal hasn't been used before, one will be created and cached for future use
     *
     * @returns {Modal.Message}
     */
    getMessageModal() {
        if (this.state.modal === undefined) {
            this.state.modal = new Modal.Message();
        }
        return this.state.modal;
    };

    /**
     * Get modal
     *
     * @readonly
     *
     * @returns {Object}
     */
    get modal() {
        return this.state.modal;
    };

    /**
     * Get and cache global search instance
     *
     * @returns {GlobalSearch}
     */
    get global_search() {
        if (this.state.global_search === null) {
            this.state.global_search = new GlobalSearch(this);
            this.state.global_search.on('search', ({term}) => {
                let search_params = new URLSearchParams(window.location.search);
                if (term !== null) {
                    search_params.set('_global_search', term);
                } else {
                    search_params.delete('_global_search');
                }
                let qs = search_params.toString(),
                    url = window.location.pathname;
                if (qs !== '') {
                    url += `?${qs}`;
                }
                window.history.replaceState(null, '', url);
            });
        }
        return this.state.global_search;
    };

    /**
     * Get restricted status
     *
     * @readonly
     * @returns {boolean}
     */
    get restricted() {
        return this.state.restricted;
    };

    /**
     * Get user
     *
     * @returns {User|null}
     */
    get user() {
        return this.state.user;
    };

    /**
     * Get title
     *
     * @returns {string|null}
     */
    get title() {
        return this.state.title;
    };

    /**
     * Get toasts controller instance
     *
     * @returns {module:NotificationToast.Controller}
     */
    get toasts() {
        return getToastController();
    };

    /**
     * Configure layout settings
     *
     * @param {object} config
     * @param {object} config.walkthrough
     */
    setConfig(config) {
        this.state.walkthrough = Object.assign(this.state.walkthrough, config.walkthrough || {});
    };

    /**
     * Set layout mode
     *
     * @param {number} mode
     */
    setMode(mode) {
        if (mode === this.state.mode) {
            return;
        }
        this.state.prev_mode = this.state.mode;
        // unset previous mode
        switch (this.state.mode) {
            case Modes.WINDOW:
                this.elem.html.removeClass('t-mode-window');
                break;
        }
        switch (mode) {
            case Modes.WINDOW:
                this.elem.html.addClass('t-mode-window');
                break;
        }
        this.state.mode = mode;
    };

    /**
     * Set mode to window
     */
    setModeWindow() {
        this.setMode(Modes.WINDOW);
    };

    /**
     * Set mode to fluid
     */
    setModeFluid() {
        this.setMode(Modes.FLUID);
    };

    setSidebarMode() {
        this.elem.header.addClass('t-no-box-shadow');
    };

    setMobileMenuMode() {
        this.elem.header.addClass('t-mobile-only-no-box-shadow');
    }

    /**
     * Restore mode to previous setting
     */
    restoreMode() {
        if (this.state.prev_mode === null) {
            return;
        }
        this.setMode(this.state.prev_mode);
        this.state.prev_mode = null;
    };

    /**
     * Set user for layout
     *
     * @param {User} user
     */
    setUser(user) {
        this.state.user = user;
        if (this.state.walkthrough.enabled) {
            const hh = this.state.helphero = helphero(this.state.walkthrough.app_id);
            let id = user.uuid;
            if (this.state.walkthrough.user_id_env !== null) {
                id = `${this.state.walkthrough.user_id_env}-${id}`;
            }
            hh.identify(id, Object.assign({env: this.state.env}, user));
        }
    };

    /**
     * Show loading overlay
     */
    showLoader() {
        if (this.state.loader_visible) {
            return;
        }
        this.elem.loader.addClass('t-show');
        this.elem.body.addClass('t-loader-active');
        this.state.loader_visible = true;
    };

    /**
     * Hide loading overlay
     */
    hideLoader() {
        if (!this.state.loader_visible) {
            return;
        }
        this.elem.loader.removeClass('t-show');
        this.elem.body.removeClass('t-loader-active');
        this.state.loader_visible = false;
    };

    /**
     * Show walkthrough on page
     */
    showWalkthrough() {
        if (this.state.helphero === undefined) {
            return;
        }
        this.state.helphero.setOptions({
            showBeacon: true
        });
    };

    /**
     * Hide walkthrough on page
     */
    hideWalkthrough() {
        if (this.state.helphero === undefined) {
            return;
        }
        this.state.helphero.setOptions({
            showBeacon: false
        });
    };

    /**
     * Update height of fixed bar
     *
     * @param {boolean} [notify=true] - Determines if we emit event
     */
    updateHeight(notify = true) {
        this.elem.spacer.toggleClass('t-with-title', this.title !== null);
        if (notify) {
            this.emit('header-height-changed');
        }
    };

    /**
     * Set layout tile
     *
     * @param {string|null} title
     * @param {boolean} [update_height=true] - Determines if we update the nav bar height because title was set/unset
     * @param {boolean} [notify=true] - Determines if title-changed event is emitted
     */
    setTitle(title, update_height = true, notify = true) {
        if (title === this.state.title) {
            return;
        }
        let prev_title = this.state.title;
        this.state.title = title;
        if (update_height && (prev_title === null || title === null)) {
            this.updateHeight(notify);
        }
        if (notify) {
            this.emit('title-changed', {title});
        }
    };

    /**
     * Close all menus which have bound to the close-menus event
     */
    closeMenus() {
        this.emit('close-menus');
    };

    /**
     * Handle current media
     *
     * @param {string[]} matches - List of matching queries
     */
    handleMedia(matches) {
        for (let name of Object.keys(Media)) {
            let config = Media[name];
            if (matches.indexOf(name) !== -1) {
                if (config.instance === undefined) {
                    config.instance = new (Media[name].type)(this);
                    config.instance.boot();
                }
                config.instance.load();
            } else if (config.instance !== undefined) {
                config.instance.unload();
            }
        }
    };

    /**
     * Boot layout
     */
    boot() {
        this.elem.html = find('html');
        this.elem.body = find('body');
        this.elem.root = find(jsSelector('layout'));
        this.elem.loader = findChild(this.elem.root, jsSelector('loader'));
        this.elem.notification = findChild(this.elem.root, jsSelector('notification'));
        this.elem.notification_text = findChild(this.elem.root, jsSelector('notification-text'));
        this.elem.header = findChild(this.elem.root, jsSelector('header'));
        this.elem.nav_bar = findChild(this.elem.header, jsSelector('nav-bar'));
        this.elem.dropdown_bar = findChild(this.elem.header, jsSelector('dropdown-bar'));
        this.elem.title_bar = findChild(this.elem.header, jsSelector('title-bar'));
        this.elem.spacer = findFirstChild(this.elem.root, jsSelector('header-spacer'));
        this.elem.content = findFirstChild(this.elem.root, jsSelector('content'));

        this.handleMedia(this.state.media_query_group.matches);

        let params = new URLSearchParams(window.location.search);
        if (params.has('_global_search')) {
            this.global_search.open(params.get('_global_search'));
        }

        onClick(window.document, () => this.closeMenus());
    };
}

event_mixin(Controller);

export const layout = new Controller();
