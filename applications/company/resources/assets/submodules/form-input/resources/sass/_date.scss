@use '~@cac-sass/base';
@use '~@cac-sass/config/color';
@use '~flatpickr/dist/themes/light';
@use '~flatpickr/dist/plugins/confirmDate/confirmDate';

$color-arrow: base.$color-grey-light-2;
$background-color: base.$color-white-default;
$color-blue: base.$color-primary-default;
$color-dark-gray: base.$color-grey-dark-3;
$color-border: base.$color-grey-light-4;
$color-white: base.$color-white-default;


.f-f-input-date-time {
    .f-fidt-input {
        cursor: pointer;
        opacity: 1;
        -webkit-text-fill-color: base.$color-grey-light-1 !important;
        color: base.$color-grey-light-1 !important;
        background-color: base.$form-input-default-bg-color;
        border-radius: base.unit-rem-calc(3px) !important;
        padding-left: base.unit-rem-calc(8px);
        border: base.unit-rem-calc(1px) solid base.$color-grey-light-3 !important;
        &:focus,
        &:active {
            border-color: base.$color-primary-light-1 !important;
            outline: base.unit-rem-calc(4px) solid base.$color-primary-light-4;
        }
    }
}

%flatpickr-arrow {
    border: none;
    &:hover {
        background: transparent;
        border: none;
    }
    &:after {
        display: inline-block;
        content: '';
        width: 6px;
        height: 6px;
        transform: rotate(-45deg);
    }
}
%flatpickr-arrow-up {
    @extend %flatpickr-arrow;
    &:after {
        border-top: 1px solid $color-arrow;
        border-right: 1px solid $color-arrow;
        border-left: none;
        border-bottom: none;
    }
}
%flatpickr-arrow-down {
    @extend %flatpickr-arrow;
    &:after {
        border-top: none;
        border-right: none;
        border-left: 1px solid $color-arrow;
        border-bottom:  1px solid $color-arrow;
    }
}

@mixin arrow-up {
    @extend %flatpickr-arrow-up;
}
@mixin arrow-down {
    @extend %flatpickr-arrow-down;
}

.flatpickr-calendar {
    width: base.unit-rem-calc(266px);
    border-radius: base.unit-rem-calc(12px);
    background-color: $background-color;
    border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
    overflow: hidden;
    margin-top: base.unit-rem-calc(2px);
    &.arrowTop:before,
    &.arrowTop:after,
    &.arrowBottom:before,
    &.arrowBottom:after
    {
        border-color: transparent;
    }
    .flatpickr-months {
        display: grid;
        grid-template-columns: base.unit-rem-calc(36px) 1fr base.unit-rem-calc(36px);
        padding: base.unit-rem-calc(8px) base.unit-rem-calc(8px) 0;
        .flatpickr-month {
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            grid-column: 2/3;
            background: $background-color;
            .flatpickr-current-month {
                @include base.typo-header($size: 16px, $line-height: base.unit-rem-calc(32px));
                padding: 0;
                color: base.$color-grey-dark-4;
                text-transform: uppercase;
                width: auto;
                text-align: left;
                left: unset;
                .cur-month {
                    margin-left: base.unit-rem-calc(12px);
                    &:hover {
                        background: transparent;
                    }
                }
                .numInputWrapper {
                    background-color: transparent;
                    > span {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: base.unit-rem-calc(10px);
                        height: base.unit-rem-calc(10px);
                        padding: base.unit-rem-calc(2px);
                        right: 0;
                        transition: opacity 0.2s ease-in-out;
                        &.arrowUp:after,
                        &.arrowDown:after {
                            border-color: $color-arrow;
                            border-width: base.unit-rem-calc(1.5px);
                            top: unset;
                        }
                    }
                    .cur-year {
                        // @todo this is to reset a default foundation style, this can be removed after foundation is gone
                        box-shadow: none;
                    }
                    .arrowUp {
                        top: base.unit-rem-calc(6px);
                        @include arrow-up;
                    }
                    .arrowDown {
                        bottom: base.unit-rem-calc(6px);
                        @include arrow-down;
                    }
                }
            }
        }
        %prev-next-arrow {
            &:hover {
                svg {
                    fill: $color-arrow;
                }
            }
            svg {
                fill: $color-arrow;
            }
        }

        .flatpickr-prev-month {
            display: flex;
            align-items: center;
            justify-content: center;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='36' height='36' fill='none'%3E%3Cg clip-path='url(%23a)'%3E%3Cpath fill='%2399AEC7' d='M14.871 17.25H24v1.5h-9.129l4.023 4.023-1.06 1.06L12 18l5.834-5.834 1.06 1.061-4.023 4.023Z'/%3E%3C/g%3E%3Cdefs%3E%3CclipPath id='a'%3E%3Cpath fill='%23fff' d='M9 9h18v18H9z'/%3E%3C/clipPath%3E%3C/defs%3E%3C/svg%3E");            border-radius: base.unit-rem-calc(36px);
            width: base.unit-rem-calc(36px);
            height: base.unit-rem-calc(36px);
            grid-column: 1/3;
            top: base.unit-rem-calc(8px);
            left: base.unit-rem-calc(8px);
            transition: all 0.2s ease-in-out;
            >svg {
                height: 0;
                width: 0;
            }
            @include base.respond-to('hover') {
                &:hover {
                    background-color: #EDF1F7;
                }
            }
        }
        .flatpickr-next-month {
            display: flex;
            align-items: center;
            justify-content: center;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='36' height='36' fill='none'%3E%3Cg clip-path='url(%23a)'%3E%3Cpath fill='%2399AEC7' d='m21.129 17.25-4.023-4.023 1.06-1.06L24 18l-5.834 5.834-1.06-1.061 4.023-4.023H12v-1.5h9.129Z'/%3E%3C/g%3E%3Cdefs%3E%3CclipPath id='a'%3E%3Cpath fill='%23fff' d='M9 9h18v18H9z'/%3E%3C/clipPath%3E%3C/defs%3E%3C/svg%3E");
            border-radius: base.unit-rem-calc(36px);
            width: base.unit-rem-calc(36px);
            height: base.unit-rem-calc(36px);
            grid-column: 3/4;
            top: base.unit-rem-calc(8px);
            right: base.unit-rem-calc(8px);
            transition: all 0.2s ease-in-out;
            >svg {
                height: 0;
                width: 0;
            }
            @include base.respond-to('hover') {
                &:hover {
                    background-color: #EDF1F7;
                }
            }
        }
    }
    &.hasTime {
        .flatpickr-time {
            border-top: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
            border-bottom: none;
            border-left: none;
            border-right: none;
        }
    }
    .flatpickr-innerContainer {
        margin-bottom: base.unit-rem-calc(8px);
        .flatpickr-weekdays {
            height: base.unit-rem-calc(32px);
            .flatpickr-weekdaycontainer {
                height: 100%;
                background: $background-color;
                gap: base.unit-rem-calc(4px);
                padding: 0 base.unit-rem-calc(8px);
            }
            .flatpickr-weekday {
                color: base.$color-grey-dark-1;
                @include base.typo-header($size: 13px, $line-height: base.unit-rem-calc(32px));
                background: $background-color;
                max-width: base.unit-rem-calc(32px);
            }
        }
        .flatpickr-days {
            border: none;
            width: base.unit-rem-calc(266px);
            .dayContainer {
                gap: base.unit-rem-calc(4px);
                width: base.unit-rem-calc(266px);
                min-width: base.unit-rem-calc(266px);
                max-width: base.unit-rem-calc(266px);
                justify-content: flex-start;
                padding: 0 base.unit-rem-calc(8px);
            }
            .flatpickr-day {
                display: flex;
                align-items: center;
                justify-content: center;
                max-width: base.unit-rem-calc(32px);
                height: base.unit-rem-calc(32px);
                border-radius: base.unit-rem-calc(24px);
                border: none;
                color: base.$color-grey-dark-4;
                transition: all 0.2s ease-in-out;
                @include base.typo-paragraph;
                @include base.respond-to('hover') {
                    &:hover {
                        color: $color-blue;
                        border: none;
                        background-color: base.$color-primary-light-4;
                    }
                }
                &.today {
                    outline: base.unit-rem-calc(2px) solid base.$color-yellow-light-1;
                }
                &.selected {
                    color: $color-white;
                    background: $color-blue;
                    border-color: $color-blue;
                }
                &.flatpickr-disabled,
                &.flatpickr-disabled:hover,
                &.prevMonthDay,
                &.nextMonthDay,
                &.notAllowed,
                &.notAllowed.prevMonthDay,
                &.notAllowed.nextMonthDay {
                    color: $color-arrow;
                }
                &.prevMonthDay {
                    text-decoration: line-through;
                }
                &.inRange {
                    box-shadow: unset;
                }
            }
        }
    }
    .flatpickr-time {
        min-height: base.unit-rem-calc(52px);
        padding: base.unit-rem-calc(8px) base.unit-rem-calc(7px);
        justify-content: space-between;
        &:after {
            display: none;
        }
        .numInputWrapper {
            flex: unset;
            height: 100%;
            width: base.unit-rem-calc(70px);
            border-radius: base.unit-rem-calc(6px);
            overflow: hidden;
            @include base.typo-header($size: 16px, $line-height: base.unit-rem-calc(35px));
            &:focus {
                background-color: base.$color-primary-light-4;
            }
            >input {
                font-size: base.unit-rem-calc(16px);
                text-align: left;
                padding-left: base.unit-rem-calc(16px);
                &:focus {
                    color: $color-blue;
                    background-color: base.$color-primary-light-4;
                }
            }
            > span {
                display: flex;
                align-items: center;
                justify-content: center;
                opacity: 1;
                width: base.unit-rem-calc(32px);
                height: base.unit-rem-calc(16px);
                padding: base.unit-rem-calc(2px);
                right: 0;
                transition: opacity 0.2s ease-in-out;
                &.arrowUp:after,
                &.arrowDown:after {
                    border-color: $color-arrow;
                    border-width: base.unit-rem-calc(1.5px);
                    top: unset;
                    &:hover {
                        border-color: $color-blue !important;
                    }
                }
            }
            .arrowUp {
                top: base.unit-rem-calc(2px) !important;
                @include arrow-up;
            }
            .arrowDown {
                top: unset !important;
                bottom: base.unit-rem-calc(2px) !important;
                @include arrow-down;
            }
        }
        .flatpickr-am-pm {
            width: base.unit-rem-calc(35px);
            height: 100%;
            border-radius: base.unit-rem-calc(6px);
            @include base.typo-header($size: 16px, $line-height: base.unit-rem-calc(35px));
            &:focus {
                color: $color-blue;
                background-color: base.$color-primary-light-4;
            }
        }
        .flatpickr-time-separator {
            width: base.unit-rem-calc(35px);
            height: 100%;
            line-height: base.unit-rem-calc(35px);
        }
    }
}
