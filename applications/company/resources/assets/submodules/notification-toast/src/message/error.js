'use strict';

import {Types} from './constants';
import {createMessage} from './factory';
import {createCloseAction} from './action/icon/close';

/**
 * Factory to create error messages with close action
 *
 * @param {string} message
 * @param {object} [config={}]
 * @returns {module:NotificationToast.Message}
 */
export function createErrorMessage(message, config = {}) {
    config.delete_after = config.delete_after ?? null;
    return createMessage(message, Types.ERROR, config).action(createCloseAction());
}
