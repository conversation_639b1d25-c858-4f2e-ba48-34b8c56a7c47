'use strict';

import core from 'mathjs/core';
import expression from 'mathjs/lib/expression';
import arithmetic from 'mathjs/lib/function/arithmetic';
import string from 'mathjs/lib/function/string';

const math = core.create();

math.import(expression);
math.import(arithmetic);
math.import(string);

const limitedEval = math.eval;

// add more security to parsing
math.import({
    'import':     function () { throw new Error('Function import is disabled') },
    'createUnit': function () { throw new Error('Function createUnit is disabled') },
    'eval':       function () { throw new Error('Function eval is disabled') },
    'parse':      function () { throw new Error('Function parse is disabled') },
    'simplify':   function () { throw new Error('Function simplify is disabled') },
    'derivative': function () { throw new Error('Function derivative is disabled') }
}, {override: true});

export default limitedEval;
