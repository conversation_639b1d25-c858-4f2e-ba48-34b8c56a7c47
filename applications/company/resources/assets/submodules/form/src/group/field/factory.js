'use strict';

import {Types} from './constants';
import {Field} from './base';
import {Text} from './text';
import {Textarea} from './textarea';
import {File} from './file';
import {Radio} from './option/radio';
import {Checkbox} from './option/checkbox';
import {Dropdown} from './option/select/dropdown';
import {ProductList} from './option/select/product_list';

const ClassMap = new Map([
    [Types.TEXT, Text],
    [Types.TEXTAREA, Textarea],
    [Types.FILE, File],
    [Types.RADIO, Radio],
    [Types.CHECKBOX, Checkbox],
    [Types.SELECT, Dropdown],
    [Types.PRODUCT_LIST, ProductList]
]);

export function getClassByType(type) {
    return ClassMap.get(type);
}

/**
 * Load all fields with their associated entry data
 *
 * @param {module:Form.Group} parent - Parent group
 * @param {Array} fields - List of fields to load
 * @param {Object.<string, object>} entries - Entry data for fields
 * @param {object} aliases
 * @returns {module:Form/Group.Field[]}
 */
export function loadAll(parent, fields, entries, aliases) {
    let instances = [];
    if (fields.length > 0) {
        for (let field of fields) {
            let type = getClassByType(field.type),
                instance = new type(parent, field, entries[field.id] || {});
            instances.push(instance);
            aliases[field.id] = instance;
            if (field.alias !== null) {
                aliases[field.alias] = instance;
            }
        }
    }
    return instances;
}

/**
 * Determine if passed value is a field instance
 *
 * @param {*} field
 * @returns {boolean}
 */
export function isField(field) {
    return field !== null && typeof field === 'object' && field instanceof Field;
}
