'use strict';

import $ from 'jquery';

import {Option} from './base';
import {Types as LayoutTypes} from '../../layout/constants';

const Tooltip = require('@ca-submodule/tooltip');

import checkbox_tpl from '@cas-form-tpl/fields/checkbox.hbs';

/**
 * @memberof module:Form/Group/Field/Option
 */
export class Checkbox extends Option {
    /**
     * Constructor
     *
     * @param {module:Form.Group} parent - Parent group
     * @param {Object} data
     * @param {Object} entry
     */
    constructor(parent, data, entry) {
        super(parent, data, entry);

        this.state.allowed_validators = ['min_count', 'max_count'];
    };

    /**
     * Get API request payload
     *
     * @returns {Object}
     */
    getPayload() {
        let payload = super.getPayload();
        payload.field_option_source = this.state.option_source;
        payload.field_option_ids = this.value === null ? [] : this.value;
        return payload;
    };

    /**
     * Boot checkbox field
     */
    async boot() {
        await super.boot();
        Tooltip.initAll(this.elem.root);

        this.elem.inputs = this.elem.root.fxFind('input');
        if (this.elem.inputs.length === 0) {
            throw new Error('Unable to find checkbox elements');
        }
        this.elem.inputs.each((i, elem) => {
            let $elem = $(elem);
            $elem.prop('checked', $.inArray($elem.attr('value'), this.state.value) !== -1);
        });
        this.elem.inputs.fxClick(() => {
            let checked = [];
            this.elem.inputs.filter(':checked').each(function () {
                checked.push($(this).val());
            });
            this.validate(checked.length === 0 ? null : checked);
        });

        this.state.booted = true;
    };

    /**
     * Render checkbox field
     *
     * @param {number} layout_type
     * @returns {string}
     */
    render(layout_type) {
        super.render();
        let classes = [];
        switch (layout_type) {
            case LayoutTypes.INPUT_TABLE_ROW:
                classes.push('t-layout-table-row');
                break;
        }
        let internal = this.config('display.is_internal', false);
        if (internal) {
            classes.push('t-internal');
        }
        return checkbox_tpl({
            path: this.path(),
            classes: classes,
            label: this.state.label,
            tooltip: this.state.tooltip,
            internal,
            columns: this.getColumns()
        });
    };
}
