'use strict';

import {Select} from './base';

/**
 * @memberof module:Form/Group/Field/Option/Select
 */
export class Dropdown extends Select {
    /**
     * Get selected option if available
     *
     * @returns {object|undefined}
     */
    get selected_option() {
        let value = this.value;
        if (value === null) {
            return undefined;
        }
        return this.option(value[0]);
    };

    /**
     * Get API request payload
     *
     * @returns {Object}
     */
    getPayload() {
        let payload = super.getPayload();
        payload.field_option_source = this.state.option_source;
        payload.field_option_ids = this.value === null ? [] : this.value;
        return payload;
    };
}
