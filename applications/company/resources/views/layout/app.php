<?php

$this->extend('layout.base');

$asset = $this->asset;

if (!$restricted) {
    $asset->manifest('common')->preload('fonts/barlow-semi-condensed/barlow-semi-condensed-v7-latin-600.woff2');
}

if ($this->shared('include_layout', true)) {
    $manifest = $asset->manifest('module:layout');
    $manifest->style("css/{$this->domain->brand()->slug}");
}
if ($this->shared('include_layout', true)) {
    $manifest->script('vendor');
    $manifest->script('module');
    $manifest->svg('sprite');
}


$has_banner_notification = !empty($banner_notifications);
$layout_class = 'l-app';
if ($has_banner_notification || $is_trial) {
    $layout_class .= ' t-has-notification';
}

$fx_pages = [
    'CUSTOMER_MANAGEMENT' => $this->uri->routeRaw('page.app.customer-management')->build() . '?cid={customer_id}',
    'PROPERTIES_MANAGEMENT' => $this->uri->routeRaw('page.app.properties')->build() . '?id={property_id}',

    'PROJECT_BID' => $this->uri->route('page.app.projects', ['path' => '']) . '/{project_id}/bids/{bid_id}',
    'PROJECT_BIDS' => $this->uri->route('page.app.projects', ['path' => '']) . '/{project_id}/bids',
    'PROJECT_SCHEDULE' => $this->uri->route('page.app.projects', ['path' => '']) . '/{project_id}/schedule',
    'PROJECT' => $this->uri->route('page.app.projects', ['path' => '']) . '/{project_id}',
    'LEAD_DETAILS' => $this->uri->route('page.app.leads', ['path' => '/details']) . '/{lead_id}',
    'TASK_DETAILS' => $this->uri->route('page.app.tasks', ['path' => '/details']) . '/{task_id}',
];
$asset->rawScript("window.fx_pages_urls = " . json_encode($fx_pages) . ";")->name('fx-pages_urls');


?>
<div class="<?= $layout_class ?>" data-js="layout">
    <div class="a-a-loader" data-js="loader"></div>

    <?php if ($is_notification_center_enabled && $has_banner_notification): ?>
        <div class="a-a-header-notification t-banner">
            <div class="a-ahn-content" data-js="banner-notification">
            <span class="a-ahnc-text" data-js="banner-notification-text">
                <?= htmlspecialchars_decode($banner_notifications[0]->summary ?? '') ?>
            </span>
            </div>
        </div>
    <?php endif; ?>

    <?php if ($is_trial): ?>
        <div class="a-a-header-notification">
            <div class="a-ahn-content" data-js="notification">
                <span class="a-ahnc-text" data-js="notification-text"></span>
            </div>
        </div>
    <?php endif; ?>

    <div class="a-a-header" data-js="header">
        <div class="a-ah-nav-bar m-layout-nav-bar<?=($is_impersonating ? ' t-warn' : '')?>" data-js="nav-bar">
            <a class="c-lnb-logo" href="/"<?=(isset($env_color) ? ' style="color:' . $env_color . '"' : '')?>>
                <?=$this->asset->embed('brand', "{$this->domain->brand()->slug}/header_logo.svg", false)?>
                <?=$this->asset->embed('brand', "{$this->domain->brand()->slug}/header_logo_mobile.svg", false)?>
            </a>
            <div class="c-lnb-nav" data-js="main-nav"></div>
            <div class="c-lnb-title-container">
                <h1 class="c-lnbtc-title" data-js="title"></h1>
            </div>

<?php if ($has_new_button): ?>
            <a class="c-lnb-new" href="" data-js="new-button">
                <svg data-icon><use xlink:href="#remix-icon--system--add-line"></use></svg>
            </a>
<?php endif; ?>
            <div class="c-lnb-actions" data-js="nav-actions">
                <a class="c-lnba-button" data-js="global-search-button">
                    <svg class="c-lnbas-icon"><use xlink:href="#remix-icon--system--search-line"></use></svg>
                </a>
                <a class="c-lnba-button" href="/">
                    <svg class="c-lnbas-icon"><use xlink:href="#remix-icon--business--calendar-line"></use></svg>
                </a>

<?php if ($is_notification_center_enabled): ?>
                <a class="c-lnba-button" id="notification-center-button" data-js="notification-center-button">
                    <svg class="c-lnbas-icon"><use xlink:href="#remix-icon--media--notification-2-line"></use></svg>
                    <?php if ($notification_center_count): ?>
                        <span class="notification-badge" id="notification-count">
                            <?php if ($notification_center_count): ?>
                                <?= $notification_center_count ?>
                            <?php endif; ?>
                        </span>
                    <?php endif; ?>
                </a>
<?php endif; ?>

<?php if ($is_primary): ?>
                <a class="c-lnba-button" href="<?=$company_profile_link?>">
                    <svg class="c-lnbas-icon"><use xlink:href="#remix-icon--system--settings-3-line"></use></svg>
                </a>
<?php endif; ?>
            </div>
            <a class="c-lnb-menu" data-js="user-menu-button">
                <div class="c-lnbm-user">
                    <svg class="c-lnbmu-icon"><use xlink:href="#remix-icon--user & faces--user-line"></use></svg>
                </div>
                <svg class="c-lnbm-arrow"><use xlink:href="#remix-icon--arrows--arrow-drop-down-line"></use></svg>
            </a>
            <a class="c-lnb-mobile-menu" data-js="mobile-menu-button">
                <div class="c-lnbmm-btn">
                    <span class="c-lnbmmb-line t-preload" data-js="mobile-menu-lines"></span>
                    <span class="c-lnbmmb-line t-preload" data-js="mobile-menu-lines"></span>
                    <span class="c-lnbmmb-line t-preload" data-js="mobile-menu-lines"></span>
                </div>
            </a>
        </div>
        <div class="a-ah-dropdown-bar m-layout-dropdown-bar" data-js="dropdown-bar"></div>
        <div class="a-ah-title-bar m-layout-title-bar" data-js="title-bar">
            <h1 class="c-ltb-title" data-js="title"></h1>
        </div>
    </div>
    <div class="a-a-header-spacer" data-js="header-spacer"></div>

    <!-- Right Sidebar -->
    <div class="notification-sidebar-drawer" style="display:none" data-js="notification-center">
        <div class="notification-sidebar s-sidebar-panel">
            <div class="ns-container">
                <div class="ns-header">
                    <span class="ns-h-title">Notifications</span>
                    <div class="c-s-form-action-menu" data-js="notification-popper-menu">
                        <a class="c-sfam-action" data-js="action" data-action="archive">
                            <svg class="c-sfama-icon"><use xlink:href="#remix-icon--business--archive-line"></use></svg>
                            <span>Archive</span>
                        </a>
                        <a class="c-sfam-action" data-js="action" data-action="settings">
                            <svg class="c-sfama-icon"><use xlink:href="#remix-icon--system--settings-3-line"></use></svg>
                            <span>Settings</span>
                        </a>
                    </div>
                    <span class="ns-h-menu" ><svg data-icon><use xlink:href="#remix-icon--system--more-line"></use></svg></span>
                </div>

                <div id="n-empty-tab" class="ns-tab active">
                    <div class="n-nsc-flex">
                        <svg class="n-nscf-icon"><use xlink:href="#remix-icon--business--inbox-line"></use></svg>
                        <span>No new notifications</span>
                    </div>
                </div>
                <div id="n-main-tab" class="ns-tab">
                    <div class="ns-read-all-container">
                        <div>
                            <svg class="loading-spinner-container"><use xlink:href="#loader--spinning-circle"></use></svg>
                        </div>
                        <div class="ns-button" data-js="read-all-button" target="_blank" href="">
                            <div data-text>Mark all as read</div>
                            <svg data-icon=""><use xlink:href="#remix-icon--system--check-double-line"></use></svg>
                        </div>
                    </div>
                    <div class="ns-wrapper"></div>
                </div>
            </div>

        </div>
    </div>

    <div class="a-a-content" data-js="content">
<?=$this->getRaw('content', '')?>
    </div>
</div>