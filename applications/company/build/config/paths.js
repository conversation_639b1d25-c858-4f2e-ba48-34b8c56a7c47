'use strict';

const path = require('path');

const paths = {
    root: path.resolve(__dirname + '/../..') + '/'
};
paths.node_modules = `${paths.root}node_modules/`;
paths.remix_icon = `${paths.node_modules}remixicon/icons/`;
paths.base = `${paths.root}resources/assets/`;
paths.css = `${paths.base}css/`;
paths.images = `${paths.base}images/`;
paths.sass = `${paths.base}sass/`;

// common paths
paths.common = {
    root: `${paths.base}common/`
};
paths.common.js = `${paths.common.root}javascript/`;
paths.common.sass = `${paths.common.root}sass/`;
paths.common.svg_symbol = `${paths.common.root}svg-symbols/`;
paths.common.icon = `${paths.common.svg_symbol}icons/`;
paths.common.loader = `${paths.common.svg_symbol}loaders/`;
paths.common.public = `${paths.common.root}public/`;

// module paths
paths.module = {
    root: `${paths.base}modules/`
};
paths.module.base = `${paths.module.root}{name}/`;
paths.module.js = `${paths.module.base}src/`;
paths.module.resources = `${paths.module.base}resources/`;
paths.module.sass = `${paths.module.resources}sass/`;
paths.module.svg_symbol = `${paths.module.resources}svg-symbols/`;
paths.module.template = `${paths.module.resources}templates/`;
paths.module.public = `${paths.module.resources}public/`;

// submodule paths
paths.submodule = {
    root: `${paths.base}submodules/`
};
paths.submodule.base = `${paths.submodule.root}{name}/`;
paths.submodule.js = `${paths.submodule.base}src/`;
paths.submodule.resources = `${paths.submodule.base}resources/`;
paths.submodule.sass = `${paths.submodule.resources}sass/`;
paths.submodule.svg_symbol = `${paths.submodule.resources}svg-symbols/`;
paths.submodule.template = `${paths.submodule.resources}templates/`;
paths.submodule.public = `${paths.submodule.resources}public/`;

// // build paths
// paths.build = {
//     base: `${paths.base}build/`
// };
// paths.build.modules = `${paths.build.base}{name}/`;

// package path
paths.package = {
    root: `${paths.base}packages/`
};

// public paths
paths.public = {
    root: `${paths.root}public/`
};
paths.public.assets = `${paths.public.root}assets/`;
paths.public.brands = `${paths.public.assets}brands/`;
paths.public.images = `${paths.public.assets}images/`;
paths.public.fonts = `${paths.public.assets}fonts/`;

paths.public.common = {
    root: `${paths.public.assets}common/`
};

paths.public.module = {
    root: `${paths.public.assets}modules/`
};
paths.public.module.base = `${paths.public.module.root}{name}/`;
paths.public.module.js = `${paths.public.module.base}js/`;
paths.public.module.css = `${paths.public.module.base}css/`;
paths.public.module.images = `${paths.public.module.base}images/`;

paths.public.submodule = {
    root: `${paths.public.assets}submodules/`
};
paths.public.submodule.base = `${paths.public.submodule.root}{name}/`;

paths.public.vendor = `${paths.public.assets}vendor/`;

paths.public.animations = `${paths.public.assets}animations/`;

// root relative url paths
const urls = {
    root: '/assets/'
};
urls.module = {
    root: `${urls.root}modules/`
};
urls.module.base = `${urls.module.root}{name}/`;
urls.module.js = `${urls.module.base}js/`;
urls.module.public = `${urls.module.base}public/`;

module.exports = {paths, urls};
