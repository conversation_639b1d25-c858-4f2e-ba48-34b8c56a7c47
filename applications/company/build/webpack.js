'use strict';

const webpack = require('webpack');
const process = require('process');

const {paths} = require('./config/paths');
const settings = require('./config/settings');
const cli = require('./utils/cli');
const filesystem = require('./utils/filesystem');
const module_util = require('./utils/module');

(async () => {
    let production = cli.getOption('mode') === 'production',
        available_modules = filesystem.getFolders(paths.module.root),
        modules = cli.getList('module'),
        available_brands = settings.brands,
        brands = cli.getList('brand');

    modules = modules !== null ? modules.filter(module => available_modules.indexOf(module) !== -1) : available_modules;
    if (modules.length === 0) {
        throw new Error('No modules assigned');
    }
    brands = brands !== null ? brands.filter(brand => available_brands.indexOf(brand) !== -1) : available_brands;
    if (brands.length === 0) {
        throw new Error('No brands assigned');
    }

    console.log(`Env: ${production ? 'Production' : 'Development'}`);
    console.log(`Modules: ${modules.join(', ')}`);
    console.log(`Brands: ${brands.join(', ')}`);

    let entries = await Promise.all(modules.map(module => module_util.getWebpackEntry(module, brands, production)));
    if (entries.length === 1) {
        return entries[0];
    }
    entries.parallelism = 2;
    return entries;
})().then(configs => {
    let compiler = webpack(configs);
    if (cli.hasOption('watch')) {
        compiler.watch(
            {
                poll: 750,
                ignored: /node_modules/
            },
            (err, stats) => {
                if (err) {
                    console.error(err.stack || err);
                    if (err.details) {
                        console.error(err.details);
                    }
                    return;
                }
                console.log(stats.toString({
                    chunks: false,
                    colors: true
                }));
            }
        );
        return;
    }
    compiler.run((err, stats) => {
        if (err) {
            console.error(err.stack || err);
            if (err.details) {
                console.error(err.details);
            }
            return;
        }
        let has_errors = stats.hasErrors();
        console.log(stats.toString({
            chunks: false,
            colors: true
        }));
        compiler.close(closeErr => {
            if (closeErr) {
                console.error(closeErr.stack || closeErr);
                if (closeErr.details) {
                    console.error(closeErr.details);
                }
                has_errors = true;
            }
            if (has_errors) {
                process.exitCode = 1;
            }
        });
    });
}, e => console.error(e));
