'use strict';

const path = require('path');
const path_type = require('../utils/path_type');

const types = {
    'remix_icon': 'remix-icon--{_path}',
    'common_icon': 'icon--{_path}',
    'common_loader': 'loader--{_path}',
    'module_svg_symbol': 'module--{name}--{_path}',
    'submodule_svg_symbol': 'submodule--{name}--{_path}'
};

/**
 * Take in SVG file name and determine proper symbol id based on path
 *
 * @param {string} file
 * @returns {string}
 */
module.exports = function (file) {
    let type = path_type(file, Object.keys(types));
    if (type !== null) {
        let parts = type.replacements._path.split(path.sep),
            last_idx = parts.length - 1;
        parts[last_idx] = path.basename(parts[last_idx], '.svg');
        type.replacements._path = parts.join('--');
        let name = types[type.name];
        for (let key of Object.keys(type.replacements)) {
            name = name.replace(`{${key}}`, type.replacements[key].toLowerCase());
        }
        return name;
    }
    throw new Error('SVG did not match app prefix');
};
