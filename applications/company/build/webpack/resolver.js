'use strict';

const path = require('path');
const aliases = require('./aliases');

/**
 * Custom plugin to resolve app module path prefixes
 */
module.exports = class {
    constructor(source, target) {
        this.source = source || 'resolve';
        this.target = target || 'resolve';
        this.aliases = Object.keys(aliases).sort((a, b) => b.length - a.length);
    };

    apply(resolver) {
        let target = resolver.ensureHook(this.target);
        resolver.getHook(this.source).tapAsync('AppResolver', (request, resolveContext, callback) => {
            if (request.request.match(/^~?@ca[cms]?-/) !== null) {
                for (let alias of this.aliases) {
                    let request_path = request.request.replace(/^~/, '');
                    if (request_path.indexOf(alias) !== 0) {
                        continue;
                    }
                    let req = Object.assign({}, request, {
                        request: path.resolve(request_path.replace(alias, aliases[alias]))
                    });
                    return resolver.doResolve(target, req, null, resolveContext, callback);
                }
            }
            callback();
        });
    };
};
