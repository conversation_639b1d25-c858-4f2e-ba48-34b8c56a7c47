const {paths} = require('../config/paths');
const filesystem = require('../utils/filesystem');

let aliases = {
    '@ca-common': paths.common.root,
    '@cac-js': paths.common.js,
    '@cac-sass': paths.common.sass,
    '@cac-icon': paths.common.icon,
    '@cac-loader': paths.common.loader,
    '@cac-public': paths.common.public,
    '@ca-submodule': paths.submodule.root,
    '@ca-package': paths.package.root
};

let modules = filesystem.getFolders(paths.module.root);
for (let module of modules) {
    aliases[`@cam-${module}-sass`] = paths.module.sass.replace('{name}', module);
    aliases[`@cam-${module}-public`] = paths.module.public.replace('{name}', module);
    aliases[`@cam-${module}-js`] = paths.module.js.replace('{name}', module);
    aliases[`@cam-${module}-tpl`] = paths.module.template.replace('{name}', module);
}

let submodules = filesystem.getFolders(paths.submodule.root);
for (let submodule of submodules) {
    aliases[`@cas-${submodule}-js`] = paths.submodule.js.replace('{name}', submodule);
    aliases[`@cas-${submodule}-sass`] = paths.submodule.sass.replace('{name}', submodule);
    aliases[`@cas-${submodule}-tpl`] = paths.submodule.template.replace('{name}', submodule);
    aliases[`@cas-${submodule}-public`] = paths.submodule.public.replace('{name}', submodule);
}

module.exports = aliases;
