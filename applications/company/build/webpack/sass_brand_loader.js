'use strict';

const path = require('path');
const queryString = require('query-string');

module.exports = function () {
    let callback = this.async(),
        {brands} = this.getOptions(),
        name = `./${path.basename(this.resourcePath)}`,
        query = queryString.parse(this.resourceQuery);
    let js = brands.map(brand => `import '${name}?${queryString.stringify({...query, brand})}';`).join('\n');
    callback(null, js);
};
