'use strict';

const path = require('path');

const {paths} = require('./config/paths');
const cli = require('./utils/cli');
const filesystem = require('./utils/filesystem');
const manifest = require('./utils/manifest');
const module_util = require('./utils/module');

let production = cli.getOption('mode') === 'production',
    promises = [],
    submodules = filesystem.getFolders(paths.submodule.root);

// copy all common files
console.log('Copying common files');
promises.push(manifest.copyFiles(path.join(paths.common.public, '**/*'), paths.public.common.root, {
    hash: production,
    cache_key: 'common'
}));

// copy any files defined in manifest or public resource directory for all submodules
console.log('Copying submodule files');
for (let submodule of submodules) {
    let submodule_path = paths.submodule.base.replace('{name}', submodule),
        public_submodule_path = paths.public.submodule.base.replace('{name}', submodule);
    module_util.getPackageInfo(submodule_path).then(info => {
        if (!Array.isArray(info.copyFiles)) {
            return;
        }
        for (let {src, dest} of info.copyFiles) {
            promises.push(filesystem.copyFiles(src, dest, {
                src_context: submodule_path,
                dest_context: public_submodule_path
            }));
        }
    });
    promises.push(manifest.copyFiles(path.join(paths.submodule.public.replace('{name}', submodule), '**/*'), public_submodule_path, {
        hash: production,
        cache_key: `submodule--${submodule}`
    }));
}
Promise.all(promises).then(() => console.log('Done'));
