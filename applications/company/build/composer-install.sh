#!/usr/bin/env bash

BASE_PATH=/var/www
TMP_PATH=$(mktemp -d -t ca-XXXXXXXXXX)

echo "Sync files to temp directory: $TMP_PATH"
cp -R $BASE_PATH/packages $TMP_PATH
mkdir -p $TMP_PATH/applications/company
if [ -d $BASE_PATH/applications/company/vendor ]; then
  echo "Copying existing vendor directory"
  cp -a $BASE_PATH/applications/company/vendor $TMP_PATH/applications/company
fi
cp $BASE_PATH/applications/company/composer.* $TMP_PATH/applications/company

cd $TMP_PATH/applications/company
echo "Running composer install"
/usr/local/bin/composer install --no-interaction
if [ "$CA_PRODUCTION" == "true" ]; then
  echo "Optimizing composer autoloader"
  /usr/local/bin/composer -o dump-autoload
fi

echo "Move files from temp to base directory"
rm -rf $BASE_PATH/applications/company/vendor
cp -a $TMP_PATH/applications/company/vendor $BASE_PATH/applications/company

echo "Cleaning up"
rm -rf $TMP_PATH
