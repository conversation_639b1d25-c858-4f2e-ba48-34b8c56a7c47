'use strict';

const fs = require('fs');
const path = require('path');
const glob = require('glob');
const webpack = require('webpack');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const MomentLocalesPlugin = require('moment-locales-webpack-plugin');
const MomentTimezoneDataPlugin = require('moment-timezone-data-webpack-plugin');
const {CleanWebpackPlugin} = require('clean-webpack-plugin');
const SpriteLoaderPlugin = require('svg-sprite-loader/plugin');
const svgToMiniDataURI = require('mini-svg-data-uri');
const TerserPlugin = require('terser-webpack-plugin');
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');
const postcssPresetEnv = require('postcss-preset-env');
const {getWebpackImporter} = require('sass-loader/dist/utils');
const queryString = require('query-string');
const sass = require('sass');

const {paths, urls} = require('../config/paths');
const settings = require('../config/settings');
const filesystem = require('./filesystem');
const manifest = require('./manifest');
const path_type = require('../utils/path_type');
const Resolver = require('../webpack/resolver');
const CallbackPlugin = require('../webpack/callback_plugin');

const AssetTypes = {
    FILE: 'file',
    SHARED_FILE: 'shared-file',
    URL: 'url',
    SVG: 'svg',
    INLINE_SVG: 'inline-svg'
}

module.exports = {
    async getPackageInfo(module_path) {
        let file = path.join(module_path, 'package.json');
        return !fs.existsSync(file) ? {} : await filesystem.readJsonFromFile(file);
    },
    getAssetConfig(module, type) {
        let config = {generator: {}};
        if ([AssetTypes.FILE, AssetTypes.SHARED_FILE, AssetTypes.URL, AssetTypes.SVG].indexOf(type) !== -1) {
            config.generator.filename = (type !== AssetTypes.SHARED_FILE ? 'public/' : '') + '[name][ext]';
            config.generator.publicPath = pathData => {
                const type_map = {
                    node_modules: {id: 'module', name: module},
                    common_public: {id: 'common', override_path: true},
                    module_public: {id: 'module'},
                    submodule_public: {id: 'submodule', override_path: true}
                };
                let full_path = pathData.module.resource,
                    type = path_type(full_path, Object.keys(type_map));
                if (type !== null) {
                    let config = type_map[type.name],
                        name = config.name ?? type.replacements.name,
                        id = name !== undefined ? `${config.id}--${name}` : config.id,
                        prefix_path = '';
                    // because this asset is from a shared manifest, we use a different path
                    if (config.override_path) {
                        prefix_path = path.dirname(type.replacements._path) + '/';
                    }
                    return `@ca-${id}:${prefix_path}`;
                }
                throw new Error(`Unable to find path type for file: ${full_path}`);
            }
        }
        if (type === AssetTypes.SHARED_FILE) {
            config.generator.emit = false;
        }
        if ([AssetTypes.URL, AssetTypes.SVG, AssetTypes.INLINE_SVG].indexOf(type) !== -1) {
            config.parser = {
                dataUrlCondition: {
                    maxSize: 4096
                }
            }
        }
        if (type === AssetTypes.SVG || type === AssetTypes.INLINE_SVG) {
            config.generator.dataUrl = content => svgToMiniDataURI(content.toString());
        }
        return config;
    },
    async getWebpackEntry(module, brands, production) {
        let module_path = paths.module.base.replace('{name}', module),
            public_module_path = paths.public.module.base.replace('{name}', module),
            module_info = await this.getPackageInfo(module_path),
            build_features = {
                svgSprite: true,
                nodeUtil: true,
                ...(module_info.buildFeatures || {})
            },
            copy_promises = [];
        if (Array.isArray(module_info.copyFiles)) {
            for (let {src, dest} of module_info.copyFiles) {
                copy_promises.push(filesystem.copyFiles(src, dest, {
                    src_context: module_path,
                    dest_context: public_module_path
                }));
            }
        }

        let svg_rules = [
                {
                    resourceQuery: /inline/,
                    type: 'asset/inline',
                    ...this.getAssetConfig(module, AssetTypes.INLINE_SVG)
                }
            ],
            plugins = [],
            provide = {},
            resolve_fallback = {};
        if (build_features.svgSprite) {
            svg_rules.push({
                test: /(node_modules\/remixicon|svg-symbols)/,
                use: [
                    {
                        loader: 'svg-sprite-loader',
                        options: {
                            symbolId: require('../webpack/svg_symbol_namer'),
                            extract: true,
                            spriteFilename: 'sprite.[hash:8].svg'
                        }
                    }
                ]
            });
            plugins.push(new SpriteLoaderPlugin({
                plainSprite: true,
                spriteAttrs: {
                    width: '0',
                    height: '0',
                    style: 'position: absolute'
                }
            }));
        }
        if (build_features.nodeUtil) {
            provide.process = 'process/browser';
            resolve_fallback.util = require.resolve('util/');
        }
        if (Object.keys(provide).length > 0) {
            plugins.push(new webpack.ProvidePlugin(provide));
        }
        svg_rules.push({
            type: 'asset',
            ...this.getAssetConfig(module, AssetTypes.SVG)
        });
        return {
            mode: production ? 'production' : 'development',
            devtool: false,
            entry: {
                module: path.join(module_path, 'module.js')
            },
            module: {
                rules: [
                    {
                        test: /\.svg$/,
                        oneOf: svg_rules
                    },
                    // url loader for module images (will inline or emit files)
                    {
                        test: /\.(png|jpg|gif|ico|cur)$/i,
                        exclude: [paths.submodule.root, paths.common.root],
                        oneOf: [
                            {
                                resourceQuery: /file/,
                                type: 'asset/resource',
                                ...this.getAssetConfig(module, AssetTypes.FILE)
                            },
                            {
                                type: 'asset',
                                ...this.getAssetConfig(module, AssetTypes.URL)
                            }
                        ]
                    },
                    // file loader for submodule and common images (will not emit files since they are copied to a central place)
                    {
                        test: /\.(png|jpg|gif|ico|ttf|woff|woff2|eot)$/i,
                        include: [paths.submodule.root, paths.common.root],
                        type: 'asset',
                        ...this.getAssetConfig(module, AssetTypes.SHARED_FILE)
                    },
                    {
                        test: /\.(sa|sc|c)ss$/,
                        oneOf: [
                            {
                                resourceQuery: /brand=/,
                                use: [
                                    {
                                        loader: MiniCssExtractPlugin.loader,
                                        options: {
                                            publicPath: public_module_path
                                        }
                                    },
                                    {
                                        loader: 'css-loader',
                                        options: {
                                            modules: false
                                        }
                                    },
                                    {
                                        loader: 'postcss-loader',
                                        options: {
                                            postcssOptions: {
                                                plugins: [postcssPresetEnv({
                                                    browsers: settings.browser_list
                                                })]
                                            }
                                        }
                                    },
                                    {
                                        loader: 'sass-loader',
                                        options: {
                                            implementation: sass,
                                            sourceMap: true,
                                            sassOptions: context => {
                                                let query = queryString.parse(context.resourceQuery),
                                                    brand = query.brand,
                                                    webpack_importer = getWebpackImporter(context, sass, [process.cwd()]);
                                                return {
                                                    importer: function (url, prev, done) {
                                                        webpack_importer.call(this, url.replace('__brand__', brand), prev, done);
                                                    },
                                                    functions: {
                                                        'currentBrand()': () => new sass.types.String(brand),
                                                        'breakpoints()': function () {
                                                            let {breakpoints} = require('../../resources/assets/common/javascript/data/responsive.json'),
                                                                keys = Object.keys(breakpoints),
                                                                map = new sass.types.Map(keys.length);
                                                            for (let i = 0; i < keys.length; i++) {
                                                                let key = keys[i];
                                                                map.setKey(i, new sass.types.String(key));
                                                                map.setValue(i, new sass.types.Number(breakpoints[key], 'px'));
                                                            }
                                                            return map;
                                                        },
                                                        'expressions()': function () {
                                                            let {expressions} = require('../../resources/assets/common/javascript/data/responsive.json'),
                                                                keys = Object.keys(expressions),
                                                                map = new sass.types.Map(keys.length);
                                                            for (let i = 0; i < keys.length; i++) {
                                                                let key = keys[i],
                                                                    value = expressions[key];
                                                                if (Array.isArray(value)) {
                                                                    value = value.join(', ');
                                                                }
                                                                map.setKey(i, new sass.types.String(key));
                                                                map.setValue(i, new sass.types.String(value));
                                                            }
                                                            return map;
                                                        }
                                                    }
                                                };
                                            }
                                        }
                                    }
                                ]
                            },
                            {
                                use: [
                                    {
                                        loader: path.resolve('./build/webpack/sass_brand_loader'),
                                        options: {brands}
                                    }
                                ]
                            }
                        ],
                    },
                    {
                        test: /\.m?js$/,
                        oneOf: [
                            {
                                resourceQuery: /worker/,
                                use: [
                                    {
                                        loader: 'worker-loader',
                                        options: {
                                            esModule: false,
                                            filename: production ? '[id].[contenthash:8].worker.js' : '[id].worker.js'
                                        }
                                    }
                                ]
                            },
                            {
                                exclude: /node_modules/,
                                use: [
                                    {
                                        loader: 'babel-loader',
                                        options: {
                                            cacheDirectory: true,
                                            presets: [
                                                ['@babel/preset-env', {targets: settings.browser_list}]
                                            ],
                                            plugins: ['@babel/plugin-transform-runtime']
                                        }
                                    },
                                    'glob-import-loader'
                                ]
                            }
                        ]
                    },
                    {
                        test: /\.(hbs|html)$/,
                        loader: 'handlebars-loader',
                        options: {
                            knownHelpers: [
                                'ifeq',
                                'formatCurrency',
                                'formatBidDate',
                                'groupByDate'
                            ]
                        }
                    }
                ]
            },
            resolve: {
                alias: {
                    'popper.js': 'popper.js/dist/umd/popper.js',
                    'decimal.js': 'decimal.js/decimal.js',
                    'dexie': 'dexie/dist/dexie.js',
                    'jquery-ui/sortable': 'jquery-ui/ui/widgets/sortable',
                    'pdfjs-web': 'pdf.js/web',
                    'pdfjs-lib': 'pdf.js/src/pdf.js'
                },
                fallback: resolve_fallback,
                plugins: [new Resolver()]
            },
            optimization: {
                minimizer: [
                    new TerserPlugin({
                        terserOptions: {
                            safari10: true
                        }
                    }),
                    new CssMinimizerPlugin({
                        minimizerOptions: {
                            preset: ['default', {
                                discardComments: {removeAll: true}
                            }]
                        }
                    })
                ],
                splitChunks: {
                    chunks: 'all',
                    cacheGroups: {
                        vendors: {
                            test: /[\\/]node_modules[\\/]/,
                            name: 'vendor'
                        },
                        module_css: {
                            name: (m) => {
                                let file = m.identifier().split('!').pop().replace(/\|0$/, ''),
                                    query = {};
                                if (file.indexOf('?') !== -1) {
                                    [, query] = file.split('?', 2);
                                    query = queryString.parse(query);
                                }
                                return `css/${query.brand || 'unbranded'}`;
                            },
                            test: (m) => m.constructor.name === 'CssModule',
                            chunks: 'all',
                            enforce: true
                        }
                    }
                }
            },
            plugins: [
                new CallbackPlugin('NotifyStart', () => console.log(`Build start: ${module}`), 'beforeRun'),
                new CleanWebpackPlugin({
                    dry: false,
                    cleanOnceBeforeBuildPatterns: ['**/*']
                }),
                new MomentLocalesPlugin(),
                new MomentTimezoneDataPlugin({
                    matchZones: ['UTC', /^America\/(New_York|Chicago|Denver|Phoenix|Los_Angeles|Anchorage|Adak)/, 'Pacific/Honolulu'],
                    startYear: 1990,
                    endYear: (new Date().getFullYear()) + 10
                }),
                new MiniCssExtractPlugin({
                    filename: '[name].css'
                }),
                new webpack.SourceMapDevToolPlugin({
                    filename: '[name][ext].map',
                    exclude: !production ? ['vendor.js'] : [],
                    columns: production
                }),
                new CallbackPlugin('CreateManifest', async () => {
                    await Promise.all(copy_promises);
                    await this.createManifest(module, production);
                }),
                ...plugins
            ],
            output: {
                filename: '[name].js',
                chunkFilename: production ? '[id].[contenthash:8].chunk.js' : '[id].chunk.js',
                path: public_module_path,
                publicPath: urls.module.base.replace('{name}', module)
            }
        };
    },
    async replaceAssetsInFile(file) {
        let data = fs.readFileSync(file, 'utf8'),
            idx = 0,
            path_promises = [];
        data = data.replace(/url\(@ca-(common|((sub)?module(--([a-z\-]+))?)):([^\)]+)\)/g, (match, ...info) => {
            path_promises.push(manifest.getPath(info[0], info[5]));
            return `{{asset-replace:${idx++}}}`;
        });
        let paths = await Promise.all(path_promises);
        for (let i in paths) {
            data = data.replace(`{{asset-replace:${i}}}`, `url(${paths[i]})`);
        }
        if (idx > 0) {
            fs.writeFileSync(file, data);
        }
    },
    async createManifest(module, hash = false) {
        let base = paths.public.module.base.replace('{name}', module);
        let files = glob.sync(path.resolve(path.join(base, '**/*.{js,css,png,jpg,svg,cur}')), {nodir: true}),
            extn_group_map = {
                png: 'assets',
                jpg: 'assets',
                svg: 'assets',
                cur: 'assets',
                css: 'css',
                js: 'js'
            },
            groups = {};
        for (let file of files) {
            if (file.match(/\.(chunk|worker)\.js$/) !== null) {
                continue;
            }
            let extn = path.extname(file).substr(1),
                group = extn_group_map[extn];
            if (group === undefined) {
                throw new Error(`Unable to find extension in group map: ${extn}`);
            }
            if (groups[group] === undefined) {
                groups[group] = [];
            }
            groups[group].push(file);
        }
        let manifest_files = {};
        await manifest.cache(`module--${module}`, manifest_files);
        if (groups.assets) {
            Object.assign(manifest_files, await manifest.renameFiles(groups.assets, base, hash));
        }
        if (groups.css) {
            let css = [];
            for (let file of groups.css) {
                css.push(this.replaceAssetsInFile(file));
            }
            await Promise.all(css);
            Object.assign(manifest_files, await manifest.renameFiles(groups.css, base, hash));
        }
        if (groups.js) {
            let js = [];
            for (let file of groups.js) {
                js.push(this.replaceAssetsInFile(file));
            }
            await Promise.all(js);
            Object.assign(manifest_files, await manifest.renameFiles(groups.js, base, hash));
        }
        await manifest.write(path.join(base, 'manifest.json'), manifest_files)
    }
};
