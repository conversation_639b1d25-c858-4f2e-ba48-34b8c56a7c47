'use strict';

const fs = require('fs');
const path = require('path');
const glob = require('glob');
const globParent = require('glob-parent');
const del = require('del');

const {paths} = require('../config/paths');
const filesystem = require('./filesystem');

module.exports = {
    __cache: {},
    path_map: {
        common: paths.public.common.root,
        module: paths.public.module.base,
        submodule: paths.public.submodule.base
    },
    getPublicPath(path) {
        if (path.indexOf(paths.public.root) !== 0) {
            throw new Error(`Path is not public: ${path}`);
        }
        return '/' + path.substr(paths.public.root.length);
    },
    async cache(name, entries = null) {
        let parts = name.split('--'),
            base_path = this.path_map[parts[0]];
        if (base_path === undefined) {
            throw new Error(`Unable to find path for manifest: ${name}`);
        }
        if (parts[1] !== undefined) {
            base_path = base_path.replace('{name}', parts[1]);
        }
        let file = path.join(base_path, 'manifest.json');
        this.__cache[name] = {
            path: file,
            public_path: this.getPublicPath(base_path),
            entries: entries || await filesystem.readJsonFromFile(file)
        };
    },
    async get(name) {
        if (this.__cache[name] === undefined) {
            await this.cache(name);
        }
        return this.__cache[name];
    },
    async getPath(name, path) {
        // get manifest from cache, otherwise find and read into cache
        let {public_path, entries} = await this.get(name);
        if (entries[path] === undefined) {
            throw new Error(`Unable to find manifest ${name} entry for: ${path}`);
        }
        return public_path + entries[path];
    },
    async create(src, hash = false) {
        let base = globParent(src),
            files = glob.sync(src, {nodir: true}),
            manifest = await filesystem.renameFiles(files, base, hash);
        await this.write(path.join(base, 'manifest.json'), manifest);
    },
    async copyFiles(src, dest, config = {}) {
        src = filesystem.parsePath(src, config.src_context || null);
        dest = filesystem.parsePath(dest, config.dest_context || null);
        if (config.clean === undefined || !!config.clean) {
            await del([path.join(dest, '**')]);
        }
        let base = globParent(src),
            matches = glob.sync(src),
            manifest = {},
            manifest_file = path.join(dest, 'manifest.json');
        if (matches.length > 0) {
            for (let match of matches) {
                let relative_path = path.relative(base, match);
                if (fs.lstatSync(match).isDirectory()) {
                    fs.mkdirSync(path.join(dest, relative_path), {recursive: true});
                    continue;
                }
                let new_relative_path = relative_path;
                if (config.hash) {
                    let dir = path.dirname(relative_path);
                    new_relative_path = `${dir}/${await filesystem.getHashedFilename(match)}`;
                }
                await filesystem.copyFile(match, path.join(dest, new_relative_path));
                manifest[relative_path] = new_relative_path;
            }
            await this.write(manifest_file, manifest);
        }
        if (config.cache_key !== undefined) {
            this.__cache[config.cache_key] = {
                path: manifest_file,
                public_path: this.getPublicPath(dest),
                entries: manifest
            };
        }
        return manifest;
    },
    async updateSourceMappingUrl(file, old_url, new_url) {
        let data = fs.readFileSync(file, 'utf8');
        data = data.replace(`//# sourceMappingURL=${old_url}`, `//# sourceMappingURL=${new_url}`);
        data = data.replace(`/*# sourceMappingURL=${old_url}*/`, `/*# sourceMappingURL=${new_url}*/`);
        fs.writeFileSync(file, data);
    },
    async renameFiles(files, base, hash = false) {
        let manifest = {};
        for (let file of files) {
            let old_path = path.relative(base, file),
                new_path = old_path;
            if (hash) {
                let dir = path.dirname(old_path),
                    hashed_name = await filesystem.getHashedFilename(file),
                    map_file = `${file}.map`;
                new_path = path.join(dir, hashed_name);
                if (fs.existsSync(map_file)) {
                    let old_map_url = `${path.basename(file)}.map`,
                        new_map_url = `${hashed_name}.map`;
                    fs.renameSync(map_file, path.join(base, dir, new_map_url));
                    await this.updateSourceMappingUrl(file, old_map_url, new_map_url);
                }
                fs.renameSync(file, path.join(base, new_path));
            }
            manifest[old_path] = new_path;
        }
        return manifest;
    },
    async write(path, files) {
        await filesystem.writeJsonToFile(path, files);
    }
};
