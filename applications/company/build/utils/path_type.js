'use strict';

const {paths} = require('../config/paths');

function get_keyed_paths(path, name = '') {
    let keyed_paths = [];
    if (typeof path === 'string') {
        let replacements = [],
            raw_path = path;
        path = path.replace(/\{([a-z\-]+)\}/g, (match, name) => {
            replacements.push(name);
            return '([a-z\-]+)';
        })
        keyed_paths.push({
            name,
            raw_path,
            path,
            replacements,
            segments: raw_path.replace(/^\/+|\/+$/g, '').split('/').length
        });
    } else if (typeof path === 'object') {
        for (let key of Object.keys(path)) {
            keyed_paths = keyed_paths.concat(get_keyed_paths(path[key], (name !== '' ? `${name}_` : '') + key));
        }
    }
    return keyed_paths;
}
let keyed_paths = get_keyed_paths(paths);
keyed_paths.sort((a, b) => b.segments - a.segments);

module.exports = function(path, allowed = null) {
    allowed = allowed !== null ? keyed_paths.filter(path => allowed.indexOf(path.name) !== -1) : keyed_paths;
    for (let path_info of allowed) {
        let match = new RegExp(`^${path_info.path}(.+)$`).exec(path);
        if (match === null) {
            continue;
        }
        let replacements = {},
            idx = 1;
        if (path_info.replacements.length > 0) {
            for (let replacement of path_info.replacements) {
                replacements[replacement] = match[idx];
                idx++;
            }
        }
        replacements._path = match[idx];
        return {name: path_info.name, replacements, info: path_info};
    }
    return null;
};
