'use strict';

const process = require('process');
const argv = require('minimist')(process.argv.slice(2));

module.exports = {
    hasOption(name) {
        return argv[name] !== undefined;
    },
    getOption(name, default_value = null) {
        return argv[name] || default_value;
    },
    getList(name, default_value = null) {
        let list = this.getOption(name);
        if (list === null) {
            return default_value;
        }
        return list.split(',');
    }
};
