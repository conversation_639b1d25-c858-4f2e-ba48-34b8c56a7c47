'use strict';

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const del = require('del');
const glob = require('glob');
const globParent = require('glob-parent');

const {paths} = require('../config/paths');

module.exports = {
    getFolders(dir) {
        return fs.readdirSync(dir)
            .filter(function(file) {
                return fs.statSync(path.join(dir, file)).isDirectory();
            });
    },
    sha1File(path) {
        return new Promise((resolve, reject) => {
            const hash = crypto.createHash('sha1'),
                rs = fs.createReadStream(path)
            rs.on('error', reject)
            rs.on('data', chunk => hash.update(chunk))
            rs.on('end', () => resolve(hash.digest('hex')))
        });
    },
    async getHashedFilename(file) {
        let extn = path.extname(file),
            name = path.basename(file, extn),
            hash = await this.sha1File(file);
        return `${name}.${hash.substr(0, 8)}${extn}`;
    },
    parsePath(p, context = null) {
        if (path.isAbsolute(p)) {
            return p;
        }
        if (p.indexOf('~') === 0) {
            p = p.substr(1);
            context = paths.node_modules;
        } else if (p.indexOf(':') !== -1) {
            let [alias, new_path] = p.split(':', 2),
                alias_path_map = {
                    'vendor': paths.public.vendor,
                    'resource-image': paths.images
                };
            if (alias_path_map[alias] !== undefined) {
                context = alias_path_map[alias];
                p = new_path;
            }
        }
        return path.resolve(context, p);
    },
    async copyFile(from, to) {
        let dir = path.dirname(to);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, {recursive: true});
        }
        fs.copyFileSync(from, to);
    },
    async copyFiles(src, dest, config = {}) {
        src = this.parsePath(src, config.src_context || null);
        dest = this.parsePath(dest, config.dest_context || null);
        if (config.clean === undefined || !!config.clean) {
            await del([path.join(dest, '**')]);
        }
        let base = globParent(src),
            matches = glob.sync(src);
        if (matches.length === 0) {
            return;
        }
        for (let match of matches) {
            let relative_path = path.relative(base, match);
            if (fs.lstatSync(match).isDirectory()) {
                fs.mkdirSync(path.join(dest, relative_path), {recursive: true});
                continue;
            }
            await this.copyFile(match, path.join(dest, relative_path));
        }
    },
    async readJsonFromFile(path) {
        let data = fs.readFileSync(path, 'utf8');
        return JSON.parse(data);
    },
    async writeJsonToFile(path, data) {
        fs.writeFileSync(path, JSON.stringify(data, null, 4));
    }
};
