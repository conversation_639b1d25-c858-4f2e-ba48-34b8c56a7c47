'use strict';

const fs = require('fs');
const path = require('path');
const sass = require('sass');
const Fiber = require('fibers');
const postcss = require('postcss');
const postcssPresetEnv = require('postcss-preset-env');
const cssnano = require('cssnano');
const del = require('del');
const glob = require('glob');
const globParent = require('glob-parent');

const {paths} = require('../config/paths');
const aliases = require('../webpack/aliases');
const settings = require('../config/settings');
const filesystem = require('./filesystem');

module.exports = {
    renderSassFile(file) {
        return new Promise((resolve, reject) => {
            sass.render({
                file,
                fiber: Fiber,
                importer: function (url, prev, done) {
                    let from = path.dirname(prev),
                        file = url,
                        to = null,
                        import_path = null,
                        match = url.match(/^~?(@ca[a-z\-]+)\/(.+)/);
                    if (match !== null) {
                        if (aliases[match[1]] !== undefined) {
                            to = aliases[match[1]];
                            import_path = match[2];
                        }
                    } else if (url[0] === '~') {
                        to = paths.node_modules;
                        import_path = url.slice(1);
                    }
                    if (to !== null) {
                        file = path.join(path.relative(from, to), import_path);
                    }
                    return done({file});
                }
            }, (err, result) => {
                if (err) {
                    reject(err);
                    return;
                }
                resolve(result.css.toString());
            });
        });
    },
    async postProcessSass(data, from, to) {
        let result = await postcss([
            postcssPresetEnv({
                browsers: settings.browser_list
            }),
            cssnano()
        ]).process(data, {from, to});
        return result.css.toString();
    },
    async toCss(from, to) {
        let result = await this.renderSassFile(from);
        result = await this.postProcessSass(result, from, to);
        let parent_dir = path.dirname(to);
        if (!fs.existsSync(parent_dir)) {
            fs.mkdirSync(parent_dir, {recursive: true});
        }
        fs.writeFileSync(to, result);
    },
    async compileFiles(src, dest, config = {}) {
        src = filesystem.parsePath(src, config.src_context || null);
        dest = filesystem.parsePath(dest, config.dest_context || null);
        await del([path.join(dest, '**')]);
        let base = globParent(src),
            matches = glob.sync(src);
        if (matches.length === 0) {
            return;
        }
        let promises = [];
        for (let match of matches) {
            if (path.basename(match).indexOf('_') === 0) {
                continue;
            }
            let relative_path = path.relative(base, match),
                basename = path.basename(relative_path, path.extname(relative_path));
            promises.push(this.toCss(match, path.resolve(path.join(dest, path.dirname(relative_path), `${basename}.css`))));
        }
        await Promise.all(promises);
    }
};
