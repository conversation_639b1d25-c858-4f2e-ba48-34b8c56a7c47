#!/usr/bin/env bash

BASE_PATH=/var/www/applications/company
TMP_PATH=$(mktemp -d -t ca-XXXXXXXXXX)

echo "Copying files to temp directory: $TMP_PATH"

echo "Copying package.json"
cp $BASE_PATH/package.json $TMP_PATH

echo "Copying yarn.lock"
cp $BASE_PATH/yarn.lock $TMP_PATH

if [ -d $BASE_PATH/node_modules ]; then
  echo "Copying node_modules"
  cp -R $BASE_PATH/node_modules $TMP_PATH/node_modules
fi

cd $TMP_PATH
echo "Running yarn install"
yarn install

echo "Clearing existing node_modules"
rm -rf $BASE_PATH/node_modules

echo "Copying from temp to base directory"
mv $TMP_PATH/node_modules $BASE_PATH/node_modules
mv $TMP_PATH/package.json $BASE_PATH
mv $TMP_PATH/yarn.lock $BASE_PATH

echo "Cleaning up"
rm -rf $TMP_PATH
