### Get current impersonated user if available
GET {{host}}/api/admin/impersonation/current

### Search for user
GET {{host}}/api/admin/impersonation/search?term={term}

### Impersonate user
POST {{host}}/api/admin/impersonation/impersonate
Content-Type: application/json

{
  "user_id": 1
}

### Stop impersonating user and return to main user
POST {{host}}/api/admin/impersonation/impersonate
Content-Type: application/json

{
  "user_id": -1
}